#!/usr/bin/env python3
"""
修复监控配置
"""
import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.models import Detection
from apps.monitor.executors import monitor_worker_handler, smart_process_check
from apps.host.models import Host

def fix_monitor_config():
    """修复监控配置"""
    print("=== 修复监控配置 ===\n")
    
    # 获取监控任务
    det = Detection.objects.filter(name='run_28k_4k_qwen2').first()
    if not det:
        print("❌ 没有找到监控任务")
        return
    
    # 获取主机
    host = Host.objects.get(pk=1)
    
    print(f"📋 当前配置:")
    print(f"   监控任务: {det.name}")
    print(f"   额外参数: {det.extra}")
    print(f"   主机: {host.name} ({host.hostname})")
    
    # 测试不同的搜索模式
    test_patterns = [
        'run_28k_4k_qwen2.sh',
        'run_28k_4k_qwen2.sh|container_first',
        'run_28k_4k_qwen2.sh|host_first',
        'run_28k_4k_qwen2',
        'run_28k_4k_qwen2|container_first'
    ]
    
    print(f"\n🔍 测试不同的搜索模式...")
    
    for pattern in test_patterns:
        print(f"\n测试模式: {pattern}")
        
        if '|' in pattern:
            process_name, search_mode = pattern.split('|', 1)
        else:
            process_name, search_mode = pattern, 'container_first'
        
        try:
            is_found, message = smart_process_check(host, process_name.strip(), search_mode.strip())
            print(f"  结果: {'✅ 找到' if is_found else '❌ 未找到'}")
            print(f"  消息: {message[:200]}...")
            
            if is_found:
                print(f"  🎯 这个模式有效！")
                
                # 更新监控任务配置
                det.extra = pattern
                det.save()
                print(f"  ✅ 已更新监控任务配置")
                
                # 运行一次完整的监控任务
                job_data = [det.id, det.type, host.id, pattern, det.threshold, det.rate * 60]
                job_json = json.dumps(job_data)
                
                print(f"  🔄 运行完整监控任务...")
                monitor_worker_handler(job_json)
                
                # 检查Redis结果
                from django_redis import get_redis_connection
                rds = get_redis_connection()
                
                process_info_key = f'spug:det:info:{det.id}_{host.id}'
                process_info_data = rds.get(process_info_key)
                
                if process_info_data:
                    try:
                        process_info = json.loads(process_info_data.decode())
                        print(f"  ✅ Redis中存储了进程信息: {len(process_info)} 个进程")
                        
                        # 更新最后运行时间
                        from datetime import datetime
                        det.latest_run_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        det.save()
                        print(f"  ✅ 更新了最后运行时间")
                        
                        return  # 成功，退出
                    except Exception as e:
                        print(f"  ❌ 解析Redis数据失败: {e}")
                else:
                    print(f"  ⚠️ Redis中没有找到进程信息")
                
                break
                
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")

if __name__ == '__main__':
    fix_monitor_config()
