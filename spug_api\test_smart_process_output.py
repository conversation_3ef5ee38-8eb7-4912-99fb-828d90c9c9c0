#!/usr/bin/env python3
"""
测试智能进程检测的输出格式
"""
import os
import sys
import django

# 设置Django环境
sys.path.insert(0, '/'.join(os.path.abspath(__file__).split('/')[:-1]))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "spug.settings")
django.setup()

from apps.host.models import Host
from apps.monitor.executors import smart_process_check

def test_smart_process_output():
    print("=== 测试智能进程检测输出格式 ===\n")
    
    try:
        host = Host.objects.get(id=5)
        print(f"连接主机: {host.name} ({host.hostname})")
        
        # 测试智能进程检测
        process_name = "run_28k_4k_qwen2.sh"
        print(f"搜索进程: {process_name}")
        
        is_found, message = smart_process_check(host, process_name, 'container_first')
        
        print(f"是否找到: {is_found}")
        print(f"返回消息:")
        print("=" * 80)
        print(message)
        print("=" * 80)
        
        # 分析消息格式
        if is_found and '找到' in message and '个匹配的进程' in message:
            print("\n📋 解析消息内容:")
            lines = message.split('\n')
            for i, line in enumerate(lines):
                print(f"行 {i+1:2d}: '{line}'")
                
        else:
            print("❌ 没有找到进程或消息格式不符合预期")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_smart_process_output()
