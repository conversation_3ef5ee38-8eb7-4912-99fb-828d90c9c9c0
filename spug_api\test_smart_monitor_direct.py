#!/usr/bin/env python3
"""
直接测试智能进程监控功能
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.executors import smart_process_check, monitor_worker_handler
from apps.host.models import Host
from django_redis import get_redis_connection
import json

def test_smart_monitor_direct():
    """直接测试智能进程监控功能"""
    print("=== 直接测试智能进程监控功能 ===\n")
    
    # 获取第一个主机进行测试
    hosts = Host.objects.filter(is_verified=True)
    if not hosts.exists():
        print("❌ 没有找到已验证的主机，请先添加并验证主机")
        return
    
    host = hosts.first()
    print(f"📡 使用主机: {host.name} ({host.hostname})")
    
    # 测试智能进程检测
    print(f"\n🔍 测试智能进程检测...")
    process_name = 'python'
    is_found, message = smart_process_check(host, process_name, 'container_first')
    
    print(f"✅ 检测结果: {'找到进程' if is_found else '未找到进程'}")
    print(f"📋 详细信息:")
    for line in message.split('\n')[:15]:  # 只显示前15行
        if line.strip():
            print(f"   {line}")
    
    # 模拟监控任务执行
    print(f"\n🎯 模拟监控任务执行...")
    task_id = 999  # 模拟任务ID
    job_data = [task_id, '6', host.id, f'{process_name}|container_first', 3, 24*60]
    job_json = json.dumps(job_data)
    
    print(f"📦 任务数据: {job_json}")
    
    try:
        # 执行监控任务
        monitor_worker_handler(job_json)
        print(f"✅ 监控任务执行成功")
        
        # 检查Redis中是否存储了进程信息
        rds = get_redis_connection()
        process_info_key = f'spug:det:info:{task_id}_{host.id}'
        process_info_data = rds.get(process_info_key)
        
        if process_info_data:
            try:
                process_info = json.loads(process_info_data.decode())
                print(f"🗄️ Redis中存储的进程信息:")
                print(f"   找到 {len(process_info)} 个进程")
                for i, proc in enumerate(process_info[:3]):
                    print(f"   进程 {i+1}: PID={proc.get('pid')}, 命令={proc.get('cmd')}, 运行时间={proc.get('etime')}")
            except Exception as e:
                print(f"❌ 解析Redis数据失败: {e}")
        else:
            print(f"⚠️ Redis中没有找到进程信息")
            
    except Exception as e:
        print(f"❌ 监控任务执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_smart_monitor_direct()
