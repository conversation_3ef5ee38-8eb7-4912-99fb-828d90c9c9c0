#!/usr/bin/env python3
"""
创建可工作的监控任务
"""
import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.models import Detection
from apps.monitor.executors import monitor_worker_handler
from apps.host.models import Host
from django_redis import get_redis_connection

def create_working_monitor():
    """创建可工作的监控任务"""
    print("=== 创建可工作的监控任务 ===\n")
    
    # 获取主机
    host = Host.objects.get(pk=1)
    print(f"🖥️ 目标主机: {host.name} ({host.hostname})")
    
    # 删除旧的监控任务
    Detection.objects.filter(name='run_28k_4k_qwen2').delete()
    print("🗑️ 删除了旧的监控任务")
    
    # 获取用户
    from apps.account.models import User
    user = User.objects.first()

    # 创建新的监控任务，使用正确的容器名称
    det = Detection.objects.create(
        name='Qwen3容器进程监控',
        type='6',  # 智能进程检测
        targets=json.dumps([host.id]),
        extra='run_28k_4k_qwen2.sh|Qwen3',  # 指定容器名称
        rate=5,
        threshold=3,
        is_active=True,
        notify_grp='[]',
        notify_mode='["4"]',
        desc='监控Qwen3容器中的run_28k_4k_qwen2.sh进程',
        group='模型服务',
        created_by=user
    )
    print(f"✅ 创建了新的监控任务: ID={det.id}")
    print(f"   名称: {det.name}")
    print(f"   额外参数: {det.extra}")
    
    # 运行监控任务
    job_data = [det.id, det.type, host.id, det.extra, det.threshold, det.rate * 60]
    job_json = json.dumps(job_data)
    
    print(f"\n🔄 运行监控任务...")
    print(f"任务数据: {job_json}")
    
    try:
        # 执行监控任务
        monitor_worker_handler(job_json)
        print("✅ 监控任务执行成功")
        
        # 更新最后运行时间
        from datetime import datetime
        det.latest_run_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        det.save()
        print("✅ 更新了最后运行时间")
        
        # 检查Redis中的数据
        rds = get_redis_connection()
        
        # 检查进程信息
        process_info_key = f'spug:det:info:{det.id}_{host.id}'
        process_info_data = rds.get(process_info_key)
        
        if process_info_data:
            try:
                process_info = json.loads(process_info_data.decode())
                print(f"✅ Redis中存储了进程信息: {len(process_info)} 个进程")
                for i, proc in enumerate(process_info[:3]):
                    print(f"   进程 {i+1}:")
                    print(f"     PID: {proc.get('pid')}")
                    print(f"     命令: {proc.get('cmd')}")
                    print(f"     运行时间: {proc.get('etime')}")
                    print(f"     位置: {proc.get('location')}")
            except Exception as e:
                print(f"❌ 解析Redis数据失败: {e}")
        else:
            print(f"⚠️ Redis中没有找到进程信息，key: {process_info_key}")
        
        # 检查监控状态
        monitor_key = f'spug:det:{det.id}'
        monitor_data = rds.hgetall(monitor_key)
        if monitor_data:
            print(f"✅ Redis中存储了监控状态: {len(monitor_data)} 个字段")
            for k, v in monitor_data.items():
                print(f"   {k.decode()}: {v.decode()}")
        else:
            print(f"⚠️ Redis中没有找到监控状态")
            
    except Exception as e:
        print(f"❌ 监控任务执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    create_working_monitor()
