/**
 * 远程日志提取组件
 * 用于从远程服务器提取日志文件并自动分析性能指标
 */
import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  Button,
  Card,
  Steps,
  Space,
  Tag,
  Progress,
  message,
  Alert,
  Table,
  Spin,
  Row,
  Col,
  Statistic,
  Typography,
  Select,
  Divider,
  Tabs,
  List,
  Checkbox,
  Empty,
  Tooltip
} from 'antd';
import {
  CloudDownloadOutlined,
  SearchOutlined,
  ExperimentOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  SaveOutlined,
  ReloadOutlined,
  PlusOutlined,
  MinusCircleOutlined,
  FolderOutlined,
  EyeOutlined,
  CloudServerOutlined
} from '@ant-design/icons';
import http from 'libs/http';
import HostSelector from '../../host/Selector';
import styles from './RemoteLogExtraction.module.less';

const { Step } = Steps;
const { Text } = Typography;
const { TextArea } = Input;

function RemoteLogExtraction({ visible, onCancel, onSuccess }) {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);

  const [extractedMetrics, setExtractedMetrics] = useState([]);
  const [extractionProgress, setExtractionProgress] = useState(0);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [selectedHostIds, setSelectedHostIds] = useState([]);
  const [extractionMode, setExtractionMode] = useState('single'); // 'single' 或 'multiple'
  const [multipleResults, setMultipleResults] = useState([]); // 存储多文件提取结果
  const [useDocker, setUseDocker] = useState(false); // 是否使用Docker容器
  const [metricsSearchText, setMetricsSearchText] = useState(''); // 指标搜索文本
  const [selectedMetricCategory, setSelectedMetricCategory] = useState('all'); // 选中的指标分类
  const [dockerContainers, setDockerContainers] = useState([]); // Docker容器列表
  const [loadingContainers, setLoadingContainers] = useState(false); // 加载容器列表状态

  // 重置状态
  const resetState = () => {
    setCurrentStep(0);
    setExtractedMetrics([]);
    setExtractionProgress(0);
    setAnalysisResult(null);
    setSelectedHostIds([]);
    form.resetFields();
  };

  // 处理主机选择
  const handleHostSelect = (hostId) => {
    // 如果是单选模式，hostId已经是单个ID而不是数组
    if (typeof hostId === 'string' || typeof hostId === 'number') {
      setSelectedHostIds([hostId]);
      form.setFieldsValue({ hostId });

      // 如果选择了Docker模式，自动获取容器列表
      if (useDocker) {
        fetchDockerContainers(hostId);
      }
    } else if (Array.isArray(hostId)) {
      // 兼容处理数组情况
      setSelectedHostIds(hostId);
      // 只取第一个主机ID，因为我们只支持单主机
      const firstHostId = hostId.length > 0 ? hostId[0] : null;
      form.setFieldsValue({ hostId: firstHostId });

      // 如果选择了Docker模式，自动获取容器列表
      if (useDocker && firstHostId) {
        fetchDockerContainers(firstHostId);
      }
    }
  };

  // 获取Docker容器列表
  const fetchDockerContainers = async (hostId) => {
    if (!hostId) return;

    try {
      setLoadingContainers(true);
      const response = await http.get('/api/exec/docker-containers/', { params: { host_id: hostId } });

      if (response.success) {
        setDockerContainers(response.containers || []);
        // 清空之前选择的容器名称
        form.setFieldsValue({ containerName: undefined });
      } else {
        message.error('获取Docker容器列表失败: ' + (response.error || '未知错误'));
        setDockerContainers([]);
      }
    } catch (error) {
      console.error('获取Docker容器列表失败:', error);
      message.error('获取Docker容器列表失败: ' + (error.response?.data?.error || error.message));
      setDockerContainers([]);
    } finally {
      setLoadingContainers(false);
    }
  };

  // 处理Docker模式切换
  const handleDockerModeChange = (value) => {
    const isDockerMode = value === 'docker';
    setUseDocker(isDockerMode);

    // 清空容器相关字段
    form.setFieldsValue({ containerName: undefined });
    setDockerContainers([]);

    // 如果切换到Docker模式且已选择主机，则获取容器列表
    if (isDockerMode && selectedHostIds.length > 0) {
      fetchDockerContainers(selectedHostIds[0]);
    }
  };

  // 步骤1: 从远程服务器获取日志文件 - 跳转到智能结果收集器
  const handleFetchLog = async () => {
    try {
      // 根据是否使用Docker来决定需要验证的字段
      const requiredFields = ['logPath', 'hostId'];
      if (useDocker) {
        requiredFields.push('containerName');
      }

      const values = await form.validateFields(requiredFields);

      // 构建跳转参数
      const params = new URLSearchParams({
        hostId: values.hostId,
        logPath: values.logPath,
        useDocker: useDocker ? '1' : '0',
        containerName: useDocker ? (values.containerName || '') : '',
        extractionMode: extractionMode,
        testPlanName: values.testPlanName || ''
      });

      // 打开新标签页跳转到智能结果收集器
      const url = `/result-extraction?${params.toString()}`;
      window.open(url, '_blank');

      // 关闭当前弹窗
      onCancel();
      message.success('已在新标签页中打开智能结果收集器');

      return; // 不再执行原来的逻辑

      if (extractionMode === 'single') {
        // 单文件模式
        const requestData = {
          log_path: values.logPath,
          host_id: values.hostId,
          use_docker: useDocker,
          container_name: useDocker ? values.containerName : null
        };

        console.log('[DEBUG] 发送请求数据:', requestData);

        const response = await http.post('/api/exec/remote-log-fetch/', requestData);

        if (response.success) {
          setExtractionProgress(50);
          setCurrentStep(1);
          message.success('日志文件获取成功');

          // 自动开始分析
          setTimeout(() => performAnalysis(response.content), 500);
        } else {
          throw new Error(response.error || '获取日志文件失败');
        }
      } else {
        // 多文件模式
        const logPaths = values.logPath.split('\n').filter(path => path.trim());
        if (logPaths.length === 0) {
          throw new Error('请输入至少一个日志文件路径');
        }

        const requestData = {
          log_paths: logPaths,
          host_id: values.hostId,
          use_docker: useDocker,
          container_name: useDocker ? values.containerName : null
        };

        console.log('[DEBUG] 多文件请求数据:', requestData);

        const response = await http.post('/api/exec/remote-log-fetch-multiple/', requestData);

        console.log('[DEBUG] 多文件响应:', response);

        if (response.success) {
          setExtractionProgress(50);
          setCurrentStep(1);
          message.success(`成功获取 ${response.results.length} 个日志文件`);

          // 自动开始分析多个文件
          setTimeout(() => performMultipleAnalysis(response.results), 500);
        } else {
          throw new Error(response.error || '获取日志文件失败');
        }
      }
    } catch (error) {
      console.error('[DEBUG] 请求失败:', error);
      console.error('[DEBUG] 错误响应:', error.response);

      const errorMessage = error.response?.data?.error || error.message || '未知错误';
      message.error('获取日志文件失败: ' + errorMessage);
      setExtractionProgress(0);
    } finally {
      setLoading(false);
    }
  };

  // 步骤2: 自动分析日志内容，提取性能指标
  const performAnalysis = async (content) => {
    try {
      setLoading(true);
      setExtractionProgress(60);

      // 使用智能算法分析日志内容
      const metrics = analyzeLogContent(content);
      setExtractedMetrics(metrics);
      setExtractionProgress(90);

      // 生成分析结果摘要
      const summary = {
        totalMetrics: metrics.length,
        highConfidenceMetrics: metrics.filter(m => m.confidence > 0.8).length,
        categories: [...new Set(metrics.map(m => m.category))]
      };
      setAnalysisResult(summary);

      setExtractionProgress(100);
      setCurrentStep(2);
      message.success(`分析完成，发现 ${metrics.length} 个性能指标`);
    } catch (error) {
      message.error('日志分析失败: ' + error.message);
      setExtractionProgress(60);
    } finally {
      setLoading(false);
    }
  };

  // 多文件分析
  const performMultipleAnalysis = async (fileResults) => {
    try {
      setLoading(true);
      setExtractionProgress(60);

      const results = [];
      let totalMetrics = 0;

      for (const fileResult of fileResults) {
        if (fileResult.success) {
          const metrics = analyzeLogContent(fileResult.content);
          results.push({
            filePath: fileResult.log_path,
            metrics: metrics,
            success: true,
            totalMetrics: metrics.length
          });
          totalMetrics += metrics.length;
        } else {
          results.push({
            filePath: fileResult.log_path,
            metrics: [],
            success: false,
            error: fileResult.error,
            totalMetrics: 0
          });
        }
      }

      setMultipleResults(results);
      setExtractionProgress(90);

      // 生成分析结果摘要
      const summary = {
        totalFiles: fileResults.length,
        successFiles: results.filter(r => r.success).length,
        totalMetrics: totalMetrics,
        highConfidenceMetrics: results.reduce((acc, r) =>
          acc + (r.metrics ? r.metrics.filter(m => m.confidence > 0.8).length : 0), 0),
        categories: [...new Set(results.flatMap(r =>
          r.metrics ? r.metrics.map(m => m.category) : []))]
      };
      setAnalysisResult(summary);

      setExtractionProgress(100);
      setCurrentStep(2);
      message.success(`分析完成，处理了 ${summary.successFiles}/${summary.totalFiles} 个文件，发现 ${totalMetrics} 个性能指标`);
    } catch (error) {
      message.error('日志分析失败: ' + error.message);
      setExtractionProgress(60);
    } finally {
      setLoading(false);
    }
  };

  // 增强的日志内容分析函数
  const analyzeLogContent = (content) => {
    const metrics = [];
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      // 模式1: 成功请求数等计数指标
      const countPattern = /([^:]+):\s*(\d+)\s*$/;
      const countMatch = line.match(countPattern);
      if (countMatch && parseFloat(countMatch[2]) > 0) {
        const label = countMatch[1].trim();
        if (label.toLowerCase().includes('request') || 
            label.toLowerCase().includes('token') ||
            label.toLowerCase().includes('duration')) {
          metrics.push({
            id: `metric_${index}_${Math.random()}`,
            label: label,
            value: countMatch[2],
            unit: getUnitFromLabel(label),
            confidence: 0.95,
            category: getCategoryFromLabel(label),
            lineNumber: index + 1,
            originalText: line.trim()
          });
        }
      }

      // 模式2: 带小数的性能指标
      const performancePattern = /([^:]+):\s*([0-9.,]+)\s*([A-Za-z/°%]+)?/;
      const perfMatch = line.match(performancePattern);
      if (perfMatch && parseFloat(perfMatch[2].replace(',', '')) > 0) {
        const label = perfMatch[1].trim();
        const value = perfMatch[2].replace(',', '');
        const unit = perfMatch[3] || getUnitFromLabel(label);
        
        if (isPerformanceMetric(label)) {
          metrics.push({
            id: `metric_${index}_${Math.random()}`,
            label: label,
            value: value,
            unit: unit,
            confidence: 0.90,
            category: getCategoryFromLabel(label),
            lineNumber: index + 1,
            originalText: line.trim()
          });
        }
      }

      // 模式3: GPU硬件信息
      const hardwarePattern = /(GPU Model|Memory Usage|GPU Temperature|Power Consumption|Model Size|Batch Size):\s*(.+)/;
      const hwMatch = line.match(hardwarePattern);
      if (hwMatch) {
        metrics.push({
          id: `metric_${index}_${Math.random()}`,
          label: hwMatch[1],
          value: hwMatch[2].replace(/[^\d.]/g, ''),
          unit: extractUnitFromValue(hwMatch[2]),
          confidence: 0.95,
          category: 'Hardware',
          lineNumber: index + 1,
          originalText: line.trim()
        });
      }
    });

    return metrics;
  };

  // 辅助函数
  const getUnitFromLabel = (label) => {
    const lowerLabel = label.toLowerCase();
    if (lowerLabel.includes('time') || lowerLabel.includes('latency') || lowerLabel.includes('ttft') || lowerLabel.includes('tpot') || lowerLabel.includes('itl')) {
      return 'ms';
    }
    if (lowerLabel.includes('throughput') || lowerLabel.includes('rate')) {
      return '/s';
    }
    if (lowerLabel.includes('token')) {
      return 'tokens';
    }
    if (lowerLabel.includes('request')) {
      return '次';
    }
    if (lowerLabel.includes('duration')) {
      return 's';
    }
    return '';
  };

  const getCategoryFromLabel = (label) => {
    const lowerLabel = label.toLowerCase();
    if (lowerLabel.includes('ttft') || lowerLabel.includes('time to first token')) {
      return 'Latency';
    }
    if (lowerLabel.includes('tpot') || lowerLabel.includes('time per output token')) {
      return 'Latency';
    }
    if (lowerLabel.includes('itl') || lowerLabel.includes('inter-token latency')) {
      return 'Latency';
    }
    if (lowerLabel.includes('throughput') || lowerLabel.includes('rate')) {
      return 'Throughput';
    }
    if (lowerLabel.includes('request')) {
      return 'Request';
    }
    if (lowerLabel.includes('token')) {
      return 'Token';
    }
    if (lowerLabel.includes('duration')) {
      return 'Time';
    }
    return 'General';
  };

  const isPerformanceMetric = (label) => {
    const keywords = ['ttft', 'tpot', 'itl', 'throughput', 'latency', 'duration', 'time', 'rate', 'mean', 'median', 'p99'];
    return keywords.some(keyword => label.toLowerCase().includes(keyword));
  };

  const extractUnitFromValue = (value) => {
    const unitMatch = value.match(/([A-Za-z°%/]+)/);
    return unitMatch ? unitMatch[1] : '';
  };

  // 筛选指标数据
  const getFilteredMetrics = (metrics) => {
    if (!metrics) return [];

    return metrics.filter(metric => {
      // 搜索文本筛选
      const matchesSearch = !metricsSearchText ||
        metric.label?.toLowerCase().includes(metricsSearchText.toLowerCase()) ||
        metric.value?.toString().toLowerCase().includes(metricsSearchText.toLowerCase());

      // 分类筛选
      const matchesCategory = selectedMetricCategory === 'all' ||
        metric.category === selectedMetricCategory;

      return matchesSearch && matchesCategory;
    });
  };

  // 获取所有可用的分类
  const getAvailableCategories = (metrics) => {
    if (!metrics) return [];
    const categories = [...new Set(metrics.map(m => m.category).filter(Boolean))];
    return categories;
  };

  // 步骤3: 保存提取结果
  const handleSaveResults = async () => {
    if (extractionMode === 'single' && extractedMetrics.length === 0) {
      message.warning('没有发现可提取的性能指标');
      return;
    }

    if (extractionMode === 'multiple' && multipleResults.length === 0) {
      message.warning('没有可保存的文件结果');
      return;
    }

    try {
      setLoading(true);
      const values = form.getFieldsValue();

      if (extractionMode === 'single') {
        // 单文件保存
        const resultData = {
          plan_name: values.planName || `日志提取_${new Date().toLocaleString()}`,
          log_path: values.logPath,
          metrics: extractedMetrics.map(metric => ({
            label: metric.label,
            value: metric.value,
            unit: metric.unit,
            confidence: metric.confidence,
            category: metric.category
          })),
          extraction_time: new Date().toISOString(),
          total_metrics: extractedMetrics.length,
          source: useDocker ? 'remote_extraction_docker' : 'remote_extraction',
          source_type: 'log_extraction',
          log_extraction_method: 'remote',
          log_source_path: values.logPath,
          log_source_host: selectedHostIds.length > 0 ? selectedHostIds[0] : null,
          ai_confidence: extractedMetrics.length > 0 ?
            extractedMetrics.reduce((acc, m) => acc + m.confidence, 0) / extractedMetrics.length : 0,
          confirmed_metrics: extractedMetrics.filter(m => m.confidence > 0.8).length
        };

        await http.post('/api/exec/test-results/', resultData);
        message.success(`成功保存 ${extractedMetrics.length} 个性能指标`);
      } else {
        // 多文件保存
        const savePromises = multipleResults
          .filter(result => result.success && result.metrics.length > 0)
          .map(result => {
            const resultData = {
              plan_name: values.planName || `日志提取_${new Date().toLocaleString()}`,
              log_path: result.filePath,
              metrics: result.metrics.map(metric => ({
                label: metric.label,
                value: metric.value,
                unit: metric.unit,
                confidence: metric.confidence,
                category: metric.category
              })),
              extraction_time: new Date().toISOString(),
              total_metrics: result.metrics.length,
              source: useDocker ? 'remote_extraction_docker_multiple' : 'remote_extraction_multiple',
              source_type: 'log_extraction',
              log_extraction_method: 'remote',
              log_source_path: result.filePath,
              log_source_host: selectedHostIds.length > 0 ? selectedHostIds[0] : null,
              ai_confidence: result.metrics.length > 0 ?
                result.metrics.reduce((acc, m) => acc + m.confidence, 0) / result.metrics.length : 0,
              confirmed_metrics: result.metrics.filter(m => m.confidence > 0.8).length
            };
            return http.post('/api/exec/test-results/', resultData);
          });

        await Promise.all(savePromises);
        const totalSaved = multipleResults.reduce((acc, r) => acc + (r.success ? r.totalMetrics : 0), 0);
        message.success(`成功保存 ${savePromises.length} 个文件的 ${totalSaved} 个性能指标`);
      }

      onSuccess();
    } catch (error) {
      message.error('保存失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 指标表格列定义
  const metricColumns = [
    {
      title: '指标名称',
      dataIndex: 'label',
      key: 'label',
      width: 200,
    },
    {
      title: '数值',
      dataIndex: 'value',
      key: 'value',
      width: 120,
      render: (value, record) => `${value} ${record.unit}`
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category) => <Tag color="blue">{category}</Tag>
    },
    {
      title: '置信度',
      dataIndex: 'confidence',
      key: 'confidence',
      width: 100,
      render: (confidence) => (
        <Progress 
          percent={Math.round(confidence * 100)} 
          size="small" 
          status={confidence > 0.8 ? 'success' : 'normal'}
        />
      )
    },
    {
      title: '行号',
      dataIndex: 'lineNumber',
      key: 'lineNumber',
      width: 80,
    }
  ];

  return (
    <Modal
      title="远程日志提取"
      visible={visible}
      onCancel={() => {
        onCancel();
        resetState();
      }}
      width={1000}
      style={{ top: 20 }}
      footer={null}
      className={styles.remoteExtractionModal}
    >
      <div className={styles.container}>
        {/* 步骤指示器 */}
        <Steps current={currentStep} className={styles.steps}>
          <Step 
            title="获取日志" 
            description="从远程服务器获取日志文件"
            icon={<CloudDownloadOutlined />}
          />
          <Step 
            title="智能分析" 
            description="自动分析并提取性能指标"
            icon={<ExperimentOutlined />}
          />
          <Step 
            title="保存结果" 
            description="确认并保存提取的指标"
            icon={<SaveOutlined />}
          />
        </Steps>

        {/* 进度指示器 */}
        {extractionProgress > 0 && (
          <Progress 
            percent={extractionProgress} 
            className={styles.progress}
            strokeColor="#722ed1"
          />
        )}

        {/* 步骤1: 日志获取配置 */}
        {currentStep === 0 && (
          <Card title="配置日志获取参数" className={styles.stepCard}>
            <Form form={form} layout="vertical">
              <Form.Item
                name="hostId"
                label="目标主机"
                rules={[{ required: true, message: '请选择目标主机' }]}
              >
                <HostSelector
                  type="button"
                  value={selectedHostIds}
                  onChange={handleHostSelect}
                  onlyOne={true}
                  title="选择目标主机"
                />
              </Form.Item>

              <Form.Item
                label="提取模式"
              >
                <Select
                  value={extractionMode}
                  onChange={setExtractionMode}
                  style={{ width: '100%' }}
                >
                  <Select.Option value="single">单文件提取</Select.Option>
                  <Select.Option value="multiple">多文件提取</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                label="文件来源"
              >
                <Select
                  value={useDocker ? 'docker' : 'host'}
                  onChange={handleDockerModeChange}
                  style={{ width: '100%' }}
                >
                  <Select.Option value="host">主机文件系统</Select.Option>
                  <Select.Option value="docker">Docker容器内</Select.Option>
                </Select>
              </Form.Item>

              {useDocker && (
                <Form.Item
                  name="containerName"
                  label="Docker容器名称"
                  rules={[{ required: true, message: '请选择Docker容器' }]}
                >
                  <Select
                    placeholder={loadingContainers ? "正在获取容器列表..." : "请选择Docker容器"}
                    loading={loadingContainers}
                    disabled={loadingContainers || dockerContainers.length === 0}
                    style={{ width: '100%' }}
                    showSearch
                    filterOption={(input, option) =>
                      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                    notFoundContent={
                      selectedHostIds.length === 0
                        ? "请先选择主机"
                        : dockerContainers.length === 0 && !loadingContainers
                          ? "未找到运行中的容器"
                          : null
                    }
                  >
                    {dockerContainers.map(container => (
                      <Select.Option key={container.name} value={container.name}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <span style={{ fontWeight: 'bold' }}>{container.name}</span>
                          <div style={{ fontSize: '12px', color: '#666', marginLeft: '8px' }}>
                            <div>{container.image}</div>
                            <div>{container.status}</div>
                          </div>
                        </div>
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              )}

              <Form.Item
                name="logPath"
                label={extractionMode === 'single'
                  ? (useDocker ? 'Docker容器内日志文件路径' : '远程日志文件路径')
                  : (useDocker ? 'Docker容器内日志文件路径（每行一个）' : '远程日志文件路径（每行一个）')
                }
                rules={[{ required: true, message: '请输入日志文件路径' }]}
                initialValue="/opt/il1024_ol31744_np16_mc2.log"
              >
                {extractionMode === 'single' ? (
                  <Input
                    placeholder={useDocker
                      ? "例如: /app/logs/application.log"
                      : "例如: /opt/il1024_ol31744_np16_mc2.log"
                    }
                    prefix={<FileTextOutlined />}
                  />
                ) : (
                  <TextArea
                    rows={4}
                    placeholder={useDocker
                      ? `请输入多个容器内日志文件路径，每行一个，例如：
/app/logs/app1.log
/app/logs/app2.log
/app/logs/app3.log`
                      : `请输入多个日志文件路径，每行一个，例如：
/opt/log1.log
/opt/log2.log
/opt/log3.log`
                    }
                  />
                )}
              </Form.Item>

              <Form.Item
                name="planName"
                label="测试计划名称（可选）"
                rules={[]}
              >
                <Input placeholder="可选，不填写将自动生成" />
              </Form.Item>

              <Alert
                message="提示"
                description={
                  useDocker
                    ? (extractionMode === 'single'
                        ? "Docker模式：系统将通过SSH连接到目标主机，然后使用docker exec命令从指定容器内获取日志文件。容器列表会在选择主机后自动获取。"
                        : "Docker多文件模式：系统将从指定容器内批量获取多个日志文件，每个文件生成独立的测试结果记录。容器列表会在选择主机后自动获取。"
                      )
                    : (extractionMode === 'single'
                        ? "请先选择目标主机，系统将通过SSH连接到该主机获取指定路径的日志文件，然后自动分析其中的性能指标。测试计划名称为可选项，不填写将自动生成。"
                        : "多文件模式：请输入多个日志文件路径，每行一个。系统将批量处理所有文件，每个文件生成独立的测试结果记录。"
                      )
                }
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            </Form>

            <div className={styles.stepActions}>
              <Button 
                type="primary"
                onClick={handleFetchLog}
                loading={loading}
                icon={<SearchOutlined />}
              >
                获取并分析日志
              </Button>
            </div>
          </Card>
        )}

        {/* 步骤2: 分析结果展示 */}
        {currentStep === 1 && (
          <Card title="日志分析中..." className={styles.stepCard}>
            <Spin spinning={loading}>
              <Alert
                message="正在分析日志内容"
                description="AI正在自动识别日志中的性能指标，请稍候..."
                type="info"
                showIcon
              />
            </Spin>
          </Card>
        )}

        {/* 步骤3: 结果确认和保存 */}
        {currentStep === 2 && (
          <div>
            {/* 分析结果摘要 */}
            <Card title="分析结果摘要" className={styles.summaryCard}>
              <Row gutter={16}>
                {extractionMode === 'multiple' && (
                  <>
                    <Col span={4}>
                      <Statistic
                        title="处理文件"
                        value={`${analysisResult?.successFiles || 0}/${analysisResult?.totalFiles || 0}`}
                        prefix={<FileTextOutlined />}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title="发现指标"
                        value={analysisResult?.totalMetrics || 0}
                        prefix={<ExperimentOutlined />}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title="高置信度"
                        value={analysisResult?.highConfidenceMetrics || 0}
                        prefix={<CheckCircleOutlined />}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title="指标类别"
                        value={analysisResult?.categories?.length || 0}
                      />
                    </Col>
                    <Col span={8}>
                      <Text strong>
                        {analysisResult?.categories?.map(cat => (
                          <Tag key={cat} color="blue">{cat}</Tag>
                        ))}
                      </Text>
                    </Col>
                  </>
                )}
                {extractionMode === 'single' && (
                  <>
                    <Col span={6}>
                      <Statistic
                        title="发现指标"
                        value={analysisResult?.totalMetrics || 0}
                        prefix={<ExperimentOutlined />}
                      />
                    </Col>
                    <Col span={6}>
                      <Statistic
                        title="高置信度"
                        value={analysisResult?.highConfidenceMetrics || 0}
                        prefix={<CheckCircleOutlined />}
                      />
                    </Col>
                    <Col span={6}>
                      <Statistic
                        title="指标类别"
                        value={analysisResult?.categories?.length || 0}
                      />
                    </Col>
                    <Col span={6}>
                      <Text strong>
                        {analysisResult?.categories?.map(cat => (
                          <Tag key={cat} color="blue">{cat}</Tag>
                        ))}
                      </Text>
                    </Col>
                  </>
                )}
              </Row>
            </Card>

            {/* 提取的指标列表 */}
            {extractionMode === 'single' ? (
              <Card
                title={
                  <Space>
                    <span>提取的性能指标</span>
                    <Tag color="blue">{getFilteredMetrics(extractedMetrics).length} / {extractedMetrics.length}</Tag>
                  </Space>
                }
                className={styles.metricsCard}
                extra={
                  <Space>
                    <Input
                      placeholder="搜索指标"
                      prefix={<SearchOutlined />}
                      value={metricsSearchText}
                      onChange={(e) => setMetricsSearchText(e.target.value)}
                      style={{ width: 150 }}
                      size="small"
                      allowClear
                    />
                    <Select
                      placeholder="分类"
                      value={selectedMetricCategory}
                      onChange={setSelectedMetricCategory}
                      style={{ width: 100 }}
                      size="small"
                    >
                      <Select.Option value="all">全部</Select.Option>
                      {getAvailableCategories(extractedMetrics).map(category => (
                        <Select.Option key={category} value={category}>
                          {category}
                        </Select.Option>
                      ))}
                    </Select>
                  </Space>
                }
              >
                <Table
                  dataSource={getFilteredMetrics(extractedMetrics)}
                  columns={metricColumns}
                  rowKey="id"
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 个指标`,
                    pageSizeOptions: ['10', '20', '50']
                  }}
                  size="small"
                  scroll={{ y: 300 }}
                />
              </Card>
            ) : (
              <Card
                title={
                  <Space>
                    <span>多文件提取结果</span>
                    <Tag color="green">{multipleResults.filter(r => r.success).length} 成功</Tag>
                    <Tag color="red">{multipleResults.filter(r => !r.success).length} 失败</Tag>
                  </Space>
                }
                className={styles.metricsCard}
                extra={
                  <Space>
                    <Input
                      placeholder="搜索指标"
                      prefix={<SearchOutlined />}
                      value={metricsSearchText}
                      onChange={(e) => setMetricsSearchText(e.target.value)}
                      style={{ width: 150 }}
                      size="small"
                      allowClear
                    />
                    <Select
                      placeholder="分类"
                      value={selectedMetricCategory}
                      onChange={setSelectedMetricCategory}
                      style={{ width: 100 }}
                      size="small"
                    >
                      <Select.Option value="all">全部</Select.Option>
                      {getAvailableCategories(multipleResults.flatMap(r => r.metrics || [])).map(category => (
                        <Select.Option key={category} value={category}>
                          {category}
                        </Select.Option>
                      ))}
                    </Select>
                  </Space>
                }
              >
                <Tabs defaultActiveKey="0" size="small">
                  {multipleResults.map((result, index) => (
                    <Tabs.TabPane
                      key={index}
                      tab={
                        <Space>
                          <FileTextOutlined />
                          <span>{result.filePath.split('/').pop()}</span>
                          {result.success ? (
                            <Tag color="green" size="small">{result.totalMetrics}</Tag>
                          ) : (
                            <Tag color="red" size="small">失败</Tag>
                          )}
                        </Space>
                      }
                    >
                      {result.success ? (
                        <Table
                          dataSource={getFilteredMetrics(result.metrics)}
                          columns={metricColumns}
                          rowKey="id"
                          pagination={{
                            pageSize: 8,
                            showSizeChanger: true,
                            showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 个指标`,
                            pageSizeOptions: ['8', '15', '30']
                          }}
                          size="small"
                          scroll={{ y: 250 }}
                        />
                      ) : (
                        <Alert
                          message="文件处理失败"
                          description={result.error}
                          type="error"
                          showIcon
                        />
                      )}
                    </Tabs.TabPane>
                  ))}
                </Tabs>
              </Card>
            )}

            {/* 操作按钮 */}
            <div className={styles.stepActions}>
              <Space>
                <Button
                  onClick={() => {
                    setCurrentStep(0);
                    setExtractedMetrics([]);
                    setMultipleResults([]);
                    setExtractionProgress(0);
                  }}
                  icon={<ReloadOutlined />}
                >
                  重新提取
                </Button>
                <Button
                  type="primary"
                  onClick={handleSaveResults}
                  loading={loading}
                  icon={<SaveOutlined />}
                  disabled={
                    extractionMode === 'single'
                      ? extractedMetrics.length === 0
                      : multipleResults.filter(r => r.success && r.metrics.length > 0).length === 0
                  }
                >
                  保存结果 ({
                    extractionMode === 'single'
                      ? extractedMetrics.length
                      : multipleResults.reduce((acc, r) => acc + (r.success ? r.totalMetrics : 0), 0)
                  })
                </Button>
              </Space>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}

export default RemoteLogExtraction;