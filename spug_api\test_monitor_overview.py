#!/usr/bin/env python3
"""
测试监控总览功能
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.models import Detection
from apps.monitor.views import get_overview
from django.test import RequestFactory
from apps.account.models import User
import json

def test_monitor_overview():
    """测试监控总览功能"""
    print("=== 监控总览功能测试 ===\n")
    
    # 创建一个模拟请求
    factory = RequestFactory()
    request = factory.get('/api/monitor/overview/')
    
    # 获取一个用户（用于权限验证）
    user = User.objects.first()
    if not user:
        print("❌ 没有找到用户，请先创建用户")
        return
    
    request.user = user
    
    # 获取监控任务列表
    detections = Detection.objects.all()
    print(f"📊 发现 {detections.count()} 个监控任务:")
    
    for det in detections:
        print(f"  - ID: {det.id}, 名称: {det.name}, 类型: {det.get_type_display()}")
        if det.type == '6':
            print(f"    智能进程检测: {det.extra}")
    
    print(f"\n🔍 调用监控总览API...")
    
    try:
        # 调用总览API
        response = get_overview(request)
        
        if hasattr(response, 'content'):
            content = response.content.decode()
            print(f"📄 API原始响应: {content[:200]}...")

            try:
                data = json.loads(content)
                print(f"✅ API调用成功，返回 {len(data)} 个监控项")

                # 显示智能进程检测的详细信息
                for i, item in enumerate(data):
                    print(f"\n📋 监控项 {i+1}:")
                    print(f"  数据类型: {type(item)}")
                    if isinstance(item, dict):
                        print(f"  名称: {item.get('name', 'N/A')}")
                        print(f"  类型: {item.get('type', 'N/A')}")
                        print(f"  目标: {item.get('target', 'N/A')}")
                        print(f"  状态: {item.get('status', 'N/A')}")
                        print(f"  最后运行: {item.get('latest_run_time', '未运行')}")

                        if item.get('type') == '智能进程检测':
                            if item.get('process_info'):
                                print(f"  进程信息: 找到 {len(item['process_info'])} 个进程")
                                for j, proc in enumerate(item['process_info'][:3]):
                                    if isinstance(proc, dict):
                                        print(f"    进程 {j+1}: PID={proc.get('pid')}, 命令={proc.get('cmd')}, 运行时间={proc.get('etime')}")
                            else:
                                print(f"  进程信息: 暂无数据")
                    else:
                        print(f"  原始数据: {item}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
        else:
            print(f"❌ API调用失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == '__main__':
    test_monitor_overview()
