import React, { useState, useEffect } from 'react';
import { useParams, useHistory } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  message,
  Spin,
  Space,
  Breadcrumb,
  Table,
  Tag
} from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  FileWordOutlined,
  SettingOutlined
} from '@ant-design/icons';
import http from '../../libs/http';

const { TextArea } = Input;
const { Option } = Select;

export default function WordTemplateEditor() {
  const { id } = useParams();
  const history = useHistory();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [template, setTemplate] = useState(null);
  const [variables, setVariables] = useState([]);

  // 自动解析模板变量
  const parseTemplateVariables = async (templateData) => {
    try {
      console.log('🔍 开始解析模板变量:', templateData.template_file);

      const res = await http.post('/api/model-storage/template-variables/parse/', {
        template_file_path: templateData.template_file
      });

      console.log('🔍 变量解析响应:', res);

      if (res && res.variables) {
        const parsedVariables = res.variables.map((variable, index) => ({
          id: variable.variable_name,
          name: variable.variable_name,
          type: variable.variable_type,
          default_value: variable.default_value || '',
          description: variable.display_name,
          required: variable.is_required || false,
          options: variable.options || [],
          group_name: variable.group_name || '其他'
        }));

        console.log('🔍 解析到的变量:', parsedVariables);
        setVariables(parsedVariables);

        // 自动保存解析的变量配置
        await saveVariablesConfig(templateData, parsedVariables);
      } else {
        console.log('⚠️ 没有解析到变量或解析失败:', res.error);
      }
    } catch (error) {
      console.error('❌ 解析模板变量失败:', error);
    }
  };

  // 保存变量配置
  const saveVariablesConfig = async (templateData, variablesList) => {
    try {
      const variables_config = {};
      variablesList.forEach(variable => {
        variables_config[variable.name] = {
          type: variable.type,
          default_value: variable.default_value,
          description: variable.description,
          required: variable.required,
          options: variable.options,
          group_name: variable.group_name
        };
      });

      await http.put('/api/model-storage/word-templates/', {
        id: templateData.id,
        variables_config: variables_config
      });

      console.log('✅ 变量配置已自动保存');
    } catch (error) {
      console.error('❌ 保存变量配置失败:', error);
    }
  };

  // 获取模板详情
  const fetchTemplate = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      const res = await http.get(`/api/model-storage/word-templates/?id=${id}`);
      
      let templateData;
      if (Array.isArray(res)) {
        templateData = res.find(t => t.id === parseInt(id));
      } else if (res.data) {
        templateData = res.data;
      } else {
        templateData = res;
      }

      if (templateData) {
        setTemplate(templateData);
        form.setFieldsValue({
          name: templateData.name,
          description: templateData.description,
          template_type: templateData.template_type,
          status: templateData.status
        });

        // 处理已有的变量配置，如果没有则自动解析
        if (templateData.variables_config && Object.keys(templateData.variables_config).length > 0) {
          console.log('🔍 使用已有的变量配置');
          const variablesList = Object.entries(templateData.variables_config).map(([key, value]) => ({
            id: key,
            name: key,
            type: value.type || 'text',
            default_value: value.default_value || '',
            description: value.description || '',
            required: value.required || false,
            options: value.options || [],
            group_name: value.group_name || '其他'
          }));
          setVariables(variablesList);
        } else {
          console.log('🔍 没有变量配置，开始自动解析');
          // 自动解析模板变量
          await parseTemplateVariables(templateData);
        }
      } else {
        message.error('模板不存在');
        history.push('/model-storage/word-templates');
      }
    } catch (error) {
      message.error('获取模板详情失败：' + error.message);
      history.push('/model-storage/word-templates');
    } finally {
      setLoading(false);
    }
  };

  // 保存模板
  const handleSave = async (values) => {
    try {
      setSaving(true);

      // 构建变量配置对象
      const variables_config = {};
      variables.forEach(variable => {
        variables_config[variable.name] = {
          type: variable.type,
          default_value: variable.default_value,
          description: variable.description,
          required: variable.required,
          options: variable.options,
          group_name: variable.group_name
        };
      });

      const data = {
        ...values,
        id: template.id,
        variables_config: variables_config
      };

      const res = await http.put('/api/model-storage/word-templates/', data);
      
      if (res.error) {
        message.error(res.error);
      } else {
        message.success('模板更新成功');
        // 更新本地数据
        setTemplate({ ...template, ...values });
      }
    } catch (error) {
      message.error('更新模板失败：' + error.message);
    } finally {
      setSaving(false);
    }
  };

  // 返回列表
  const handleBack = () => {
    history.push('/model-storage/word-templates');
  };



  // 更新变量默认值
  const updateVariableDefaultValue = (variableName, value) => {
    setVariables(prevVariables =>
      prevVariables.map(variable =>
        variable.name === variableName
          ? { ...variable, default_value: value }
          : variable
      )
    );
  };

  // 变量表格列定义
  const variableColumns = [
    {
      title: '变量名',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (name) => <code>${name}</code>
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type) => {
        const typeMap = {
          text: '文本',
          textarea: '多行文本',
          richtext: '富文本',
          number: '数字',
          date: '日期',
          select: '选择',
          gpu_model: 'GPU型号',
          vendor: '厂商'
        };
        const colorMap = {
          text: 'blue',
          textarea: 'cyan',
          richtext: 'purple',
          number: 'green',
          date: 'orange',
          select: 'gold',
          gpu_model: 'red',
          vendor: 'magenta'
        };
        return <Tag color={colorMap[type] || 'blue'}>{typeMap[type] || type}</Tag>;
      }
    },
    {
      title: '分组',
      dataIndex: 'group_name',
      key: 'group_name',
      width: 120,
      render: (group) => <Tag color="geekblue">{group}</Tag>
    },
    {
      title: '默认值',
      dataIndex: 'default_value',
      key: 'default_value',
      width: 250,
      render: (value, record) => {
        if (record.type === 'richtext') {
          return (
            <TextArea
              value={value}
              onChange={(e) => updateVariableDefaultValue(record.name, e.target.value)}
              placeholder="请输入富文本内容..."
              rows={3}
              style={{ fontSize: '12px' }}
            />
          );
        } else if (record.type === 'textarea') {
          return (
            <TextArea
              value={value}
              onChange={(e) => updateVariableDefaultValue(record.name, e.target.value)}
              placeholder="请输入多行文本..."
              rows={2}
              style={{ fontSize: '12px' }}
            />
          );
        } else if (record.type === 'select') {
          return (
            <Select
              value={value}
              onChange={(val) => updateVariableDefaultValue(record.name, val)}
              placeholder="请选择..."
              style={{ width: '100%' }}
              size="small"
            >
              {(record.options || []).map(option => (
                <Option key={option} value={option}>{option}</Option>
              ))}
            </Select>
          );
        } else {
          return (
            <Input
              value={value}
              onChange={(e) => updateVariableDefaultValue(record.name, e.target.value)}
              placeholder="请输入默认值..."
              size="small"
            />
          );
        }
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    }
  ];

  useEffect(() => {
    fetchTemplate();
  }, [id]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>加载模板信息中...</p>
      </div>
    );
  }

  if (!template) {
    return null;
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 面包屑导航 */}
      <Breadcrumb style={{ marginBottom: 24 }}>
        <Breadcrumb.Item>
          <a onClick={handleBack}>Word模板管理</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>编辑模板</Breadcrumb.Item>
      </Breadcrumb>

      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={handleBack}
          >
            返回列表
          </Button>
          <FileWordOutlined style={{ fontSize: 24, color: '#1890ff' }} />
          <h2 style={{ margin: 0 }}>编辑模板：{template.name}</h2>
        </Space>
      </div>

      {/* 编辑表单 */}
      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          size="large"
        >
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="模板名称"
                rules={[{ required: true, message: '请输入模板名称' }]}
              >
                <Input 
                  placeholder="请输入模板名称" 
                  size="large"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="template_type"
                label="模板类型"
                rules={[{ required: true, message: '请选择模板类型' }]}
              >
                <Select 
                  placeholder="请选择模板类型"
                  size="large"
                >
                  <Option value="test_guide">测试指导</Option>
                  <Option value="report">测试报告</Option>
                  <Option value="manual">使用手册</Option>
                  <Option value="specification">技术规范</Option>
                  <Option value="imported">导入模板</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select 
                  placeholder="请选择状态"
                  size="large"
                >
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                  <Option value="draft">草稿</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="创建时间">
                <Input 
                  value={template.created_at} 
                  disabled 
                  size="large"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="模板描述"
          >
            <TextArea 
              rows={6} 
              placeholder="请输入模板描述"
              size="large"
            />
          </Form.Item>

          <Form.Item label="模板文件路径">
            <Input 
              value={template.template_file} 
              disabled 
              size="large"
            />
          </Form.Item>

          <Form.Item label="使用次数">
            <Input 
              value={template.usage_count || 0} 
              disabled 
              size="large"
            />
          </Form.Item>

          {/* 操作按钮 */}
          <Form.Item style={{ marginTop: 32 }}>
            <Space size="large">
              <Button 
                type="primary" 
                htmlType="submit" 
                icon={<SaveOutlined />}
                loading={saving}
                size="large"
              >
                保存更改
              </Button>
              <Button 
                onClick={handleBack}
                size="large"
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 模板变量 */}
      <Card
        title={
          <Space>
            <SettingOutlined />
            <span>模板变量</span>
            <Tag color="blue">{variables.length} 个变量</Tag>
          </Space>
        }
        extra={
          variables.length > 0 && (
            <Tag color="green">已自动解析</Tag>
          )
        }
        style={{ marginTop: 24 }}
      >
        {variables.length > 0 ? (
          <>
            <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
              <Space>
                <span style={{ color: '#52c41a', fontWeight: 'bold' }}>💡 提示：</span>
                <span>系统已自动从Word文档中解析出变量，您可以直接编辑默认值。修改后点击"保存更改"按钮保存。</span>
              </Space>
            </div>
            <Table
              columns={variableColumns}
              dataSource={variables}
              rowKey="name"
              pagination={false}
              size="middle"
              scroll={{ x: 1000 }}
            />
          </>
        ) : (
          <div style={{ textAlign: 'center', padding: 40, color: '#999' }}>
            <FileWordOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div>暂未解析到模板变量</div>
            <div style={{ fontSize: 12, marginTop: 8 }}>
              请确保Word文档中包含 ${'${变量名}'} 格式的变量
            </div>
          </div>
        )}
      </Card>


    </div>
  );
}
