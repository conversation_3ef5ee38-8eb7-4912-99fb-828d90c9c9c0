<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .card {
            border: 2px solid #e8e8e8;
            border-radius: 12px;
            padding: 16px;
            margin: 8px;
            width: 200px;
            height: 100px;
            display: inline-block;
            vertical-align: top;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.12);
        }
        .card.running {
            background: #f6ffed;
            border-color: #52c41a;
        }
        .card.stopped {
            background: #fff2f0;
            border-color: #ff4d4f;
        }
        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .card-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
        }
        .status-dot.running {
            background-color: #52c41a;
            box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
        }
        .status-dot.stopped {
            background-color: #ff4d4f;
            box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
        }
        .status-text {
            font-size: 14px;
            color: #595959;
        }
        .process-info {
            margin-top: 8px;
            font-size: 12px;
            color: #8c8c8c;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #999;
        }
        .error {
            color: #ff4d4f;
            background: #fff2f0;
            padding: 16px;
            border-radius: 4px;
            margin: 16px 0;
        }
        .debug-info {
            background: #f0f0f0;
            padding: 16px;
            border-radius: 4px;
            margin: 16px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>监控API测试</h1>
        <div id="loading" class="loading">正在加载监控数据...</div>
        <div id="error" class="error" style="display: none;"></div>
        <div id="debug" class="debug-info" style="display: none;"></div>
        <div id="cards"></div>
    </div>

    <script>
        // 模拟token（实际应用中应该从localStorage获取）
        const token = 'your-token-here';
        
        async function fetchMonitorData() {
            try {
                const response = await fetch('/api/monitor/overview/', {
                    method: 'GET',
                    headers: {
                        'X-Token': token,
                        'Content-Type': 'application/json'
                    }
                });
                
                const result = await response.json();
                
                // 显示调试信息
                document.getElementById('debug').style.display = 'block';
                document.getElementById('debug').textContent = `API响应:\n${JSON.stringify(result, null, 2)}`;
                
                if (result.error) {
                    throw new Error(result.error);
                }
                
                const data = result.data || [];
                renderCards(data);
                
            } catch (error) {
                console.error('获取监控数据失败:', error);
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').textContent = `错误: ${error.message}`;
            } finally {
                document.getElementById('loading').style.display = 'none';
            }
        }
        
        function renderCards(data) {
            const cardsContainer = document.getElementById('cards');
            
            if (data.length === 0) {
                cardsContainer.innerHTML = '<div class="loading">暂无监控项</div>';
                return;
            }
            
            cardsContainer.innerHTML = data.map(item => {
                const isRunning = item.status === '1' || item.status === '2';
                const processCount = item.process_info ? item.process_info.length : 0;
                
                return `
                    <div class="card ${isRunning ? 'running' : 'stopped'}" title="${item.desc || ''}">
                        <div class="card-title">${item.name}</div>
                        <div class="card-status">
                            <div class="status-dot ${isRunning ? 'running' : 'stopped'}"></div>
                            <div class="status-text">${isRunning ? '运行中' : '未运行'}</div>
                        </div>
                        <div class="process-info">
                            ${processCount > 0 ? `${processCount} 个进程` : '无进程信息'}
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        // 页面加载时获取数据
        fetchMonitorData();
        
        // 每5秒自动刷新
        setInterval(fetchMonitorData, 5000);
    </script>
</body>
</html>
