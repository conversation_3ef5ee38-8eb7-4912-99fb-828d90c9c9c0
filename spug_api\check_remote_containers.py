#!/usr/bin/env python3
"""
检查远程主机的容器情况
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.host.models import Host

def check_remote_containers():
    """检查远程主机的容器情况"""
    print("=== 检查远程主机的容器情况 ===\n")
    
    # 获取主机
    host = Host.objects.get(pk=1)
    print(f"🖥️ 目标主机: {host.name} ({host.hostname})")
    
    try:
        with host.get_ssh() as ssh:
            # 检查各种容器运行时
            container_runtimes = [
                ('docker', 'docker ps'),
                ('podman', 'podman ps'),
                ('containerd', 'ctr containers list'),
                ('lxc', 'lxc list'),
            ]
            
            for runtime, command in container_runtimes:
                print(f"\n🔍 检查 {runtime}...")
                exit_code, out = ssh.exec_command_raw(f"which {runtime}")
                if exit_code == 0:
                    print(f"  ✅ 找到 {runtime}: {out.strip()}")
                    
                    # 尝试列出容器
                    exit_code, out = ssh.exec_command_raw(command)
                    print(f"  容器列表 (退出码: {exit_code}):")
                    if out.strip():
                        print(f"    {out.strip()}")
                    else:
                        print(f"    无输出")
                else:
                    print(f"  ❌ 未找到 {runtime}")
            
            # 检查进程中是否有容器相关的
            print(f"\n🔍 检查运行中的进程...")
            exit_code, out = ssh.exec_command_raw("ps aux | grep -E '(docker|containerd|podman|lxc)' | grep -v grep")
            if out.strip():
                print(f"容器相关进程:")
                for line in out.strip().split('\n'):
                    print(f"  {line}")
            else:
                print(f"未找到容器相关进程")
            
            # 直接搜索目标进程
            print(f"\n🎯 直接搜索目标进程...")
            exit_code, out = ssh.exec_command_raw("ps aux | grep run_28k_4k_qwen2 | grep -v grep")
            if out.strip():
                print(f"找到目标进程:")
                for line in out.strip().split('\n'):
                    print(f"  {line}")
                    
                # 解析进程信息
                parts = line.strip().split(None, 10)
                if len(parts) >= 11:
                    print(f"  进程详情:")
                    print(f"    用户: {parts[0]}")
                    print(f"    PID: {parts[1]}")
                    print(f"    命令: {' '.join(parts[10:])}")
            else:
                print(f"未找到目标进程")
            
            # 检查是否有其他相关进程
            print(f"\n🔍 搜索相关进程...")
            search_patterns = ['qwen', 'python', 'bash.*run', 'sh.*run']
            
            for pattern in search_patterns:
                exit_code, out = ssh.exec_command_raw(f"ps aux | grep -E '{pattern}' | grep -v grep")
                if out.strip():
                    print(f"模式 '{pattern}' 匹配的进程:")
                    for line in out.strip().split('\n')[:5]:  # 只显示前5个
                        print(f"  {line}")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_remote_containers()
