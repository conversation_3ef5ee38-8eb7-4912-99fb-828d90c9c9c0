#!/usr/bin/env python3
"""
测试容器搜索
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.executors import get_running_containers, search_process_in_container
from apps.host.models import Host

def test_container_search():
    """测试容器搜索"""
    print("=== 测试容器搜索 ===\n")
    
    # 获取主机
    host = Host.objects.get(pk=1)
    print(f"🖥️ 目标主机: {host.name} ({host.hostname})")
    
    # 获取运行中的容器
    print(f"\n📦 获取运行中的容器...")
    containers = get_running_containers(host)
    print(f"发现 {len(containers)} 个运行中的容器:")
    for container in containers:
        print(f"  - {container}")
    
    # 在每个容器中搜索进程
    process_patterns = ['run_28k_4k_qwen2.sh', 'run_28k_4k_qwen2', 'bash', 'python']
    
    for container in containers:
        print(f"\n🔍 在容器 '{container}' 中搜索进程...")
        
        for pattern in process_patterns:
            print(f"\n  搜索模式: '{pattern}'")
            
            try:
                is_found, result = search_process_in_container(host, container, pattern)
                
                if is_found and result:
                    print(f"  ✅ 找到 {len(result)} 个进程:")
                    for i, proc in enumerate(result):
                        print(f"    进程 {i+1}:")
                        print(f"      PID: {proc['pid']}")
                        print(f"      命令: {proc['cmd']}")
                        print(f"      运行时间: {proc['etime']}")
                        print(f"      位置: {proc['location']}")
                else:
                    print(f"  ❌ 未找到进程")
                    if isinstance(result, str):
                        print(f"  错误信息: {result}")
                        
            except Exception as e:
                print(f"  ❌ 搜索失败: {e}")
    
    # 直接测试命令
    print(f"\n🔧 直接测试Docker命令...")
    
    try:
        with host.get_ssh() as ssh:
            # 测试容器列表
            exit_code, out = ssh.exec_command_raw("docker ps --format '{{.Names}}'")
            print(f"Docker ps 命令结果 (退出码: {exit_code}):")
            print(f"输出: {repr(out)}")
            
            # 测试在Qwen3容器中搜索进程
            command = "docker exec Qwen3 ps -eo pid,ppid,cmd,etime,pcpu,pmem --no-headers 2>/dev/null | grep -v grep | grep 'run_28k_4k_qwen2'"
            exit_code, out = ssh.exec_command_raw(command)
            print(f"\n进程搜索命令结果 (退出码: {exit_code}):")
            print(f"命令: {command}")
            print(f"输出: {repr(out)}")
            
            if out.strip():
                print("解析进程信息:")
                for line in out.strip().split('\n'):
                    if line.strip():
                        parts = line.strip().split(None, 5)
                        print(f"  分割结果: {parts}")
                        if len(parts) >= 6:
                            print(f"    PID: {parts[0]}")
                            print(f"    PPID: {parts[1]}")
                            print(f"    CMD: {parts[2]}")
                            print(f"    ETIME: {parts[3]}")
                            print(f"    PCPU: {parts[4]}")
                            print(f"    PMEM: {parts[5]}")
            
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_container_search()
