#!/usr/bin/env python3
"""
测试智能进程监控功能
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.host.models import Host
from apps.monitor.executors import smart_process_check, get_running_containers, search_process_in_container, search_process_on_host

def test_smart_process_monitor():
    """测试智能进程监控功能"""
    print("=== 智能进程监控功能测试 ===\n")
    
    # 获取第一个主机进行测试
    hosts = Host.objects.filter(is_verified=True)
    if not hosts.exists():
        print("❌ 没有找到已验证的主机，请先添加并验证主机")
        return
    
    host = hosts.first()
    print(f"📡 使用主机: {host.name} ({host.hostname})")
    
    # 测试1: 获取容器列表
    print("\n1️⃣ 测试获取容器列表...")
    containers = get_running_containers(host)
    print(f"   发现 {len(containers)} 个运行中的容器: {containers}")
    
    # 测试2: 在容器中搜索进程（如果有容器的话）
    if containers:
        print(f"\n2️⃣ 测试在容器中搜索进程...")
        container_name = containers[0]
        print(f"   在容器 {container_name} 中搜索 'sh' 进程...")
        is_found, result = search_process_in_container(host, container_name, 'sh')
        if is_found:
            print(f"   ✅ 找到 {len(result)} 个进程")
            for proc in result[:2]:  # 只显示前2个
                print(f"      PID: {proc['pid']}, 命令: {proc['cmd']}, 运行时间: {proc['etime']}")
        else:
            print(f"   ❌ 未找到进程: {result}")
    
    # 测试3: 在主机上搜索进程
    print(f"\n3️⃣ 测试在主机上搜索进程...")
    print(f"   在主机上搜索 'python' 进程...")
    is_found, result = search_process_on_host(host, 'python')
    if is_found:
        print(f"   ✅ 找到 {len(result)} 个进程")
        for proc in result[:2]:  # 只显示前2个
            print(f"      PID: {proc['pid']}, 命令: {proc['cmd']}, 运行时间: {proc['etime']}")
    else:
        print(f"   ❌ 未找到进程: {result}")
    
    # 测试4: 智能进程检测
    print(f"\n4️⃣ 测试智能进程检测...")
    test_processes = ['python', 'nginx', 'nonexistent_process']
    
    for process_name in test_processes:
        print(f"\n   搜索进程: {process_name}")
        is_found, message = smart_process_check(host, process_name, 'container_first')
        status = "✅ 找到" if is_found else "❌ 未找到"
        print(f"   {status}")
        print(f"   结果详情:")
        for line in message.split('\n')[:10]:  # 只显示前10行
            if line.strip():
                print(f"      {line}")
        if len(message.split('\n')) > 10:
            print("      ...")

if __name__ == '__main__':
    test_smart_process_monitor()
