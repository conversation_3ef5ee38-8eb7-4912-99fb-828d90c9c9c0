#!/usr/bin/env python3
"""
运行正确的监控任务
"""
import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.models import Detection
from apps.monitor.executors import monitor_worker_handler
from apps.host.models import Host
from django_redis import get_redis_connection

def run_correct_monitor():
    """运行正确的监控任务"""
    print("=== 运行正确的监控任务 ===\n")
    
    # 获取监控任务
    det = Detection.objects.filter(name='run_28k_4k_qwen2').first()
    if not det:
        print("❌ 没有找到监控任务")
        return
    
    # 获取目标主机
    targets = json.loads(det.targets)
    host_id = targets[0]
    
    print(f"📋 监控任务信息:")
    print(f"   ID: {det.id}")
    print(f"   名称: {det.name}")
    print(f"   目标主机ID: {host_id}")
    print(f"   监控类型: {det.type}")
    print(f"   额外参数: {det.extra}")
    
    # 构建正确的任务数据
    job_data = [det.id, det.type, host_id, det.extra, det.threshold, det.rate * 60]
    job_json = json.dumps(job_data)
    
    print(f"\n🔄 执行监控任务...")
    print(f"任务数据: {job_json}")
    
    try:
        # 执行监控任务
        monitor_worker_handler(job_json)
        print("✅ 监控任务执行成功")
        
        # 更新最后运行时间
        from datetime import datetime
        det.latest_run_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        det.save()
        print("✅ 更新了最后运行时间")
        
        # 检查Redis中的数据
        rds = get_redis_connection()
        
        # 检查进程信息
        process_info_key = f'spug:det:info:{det.id}_{host_id}'
        process_info_data = rds.get(process_info_key)
        
        if process_info_data:
            try:
                process_info = json.loads(process_info_data.decode())
                print(f"✅ Redis中存储了进程信息: {len(process_info)} 个进程")
                for i, proc in enumerate(process_info[:3]):
                    print(f"   进程 {i+1}: PID={proc.get('pid')}, 运行时间={proc.get('etime')}, 命令={proc.get('cmd')}")
            except Exception as e:
                print(f"❌ 解析Redis数据失败: {e}")
        else:
            print(f"⚠️ Redis中没有找到进程信息，key: {process_info_key}")
        
        # 检查监控状态
        monitor_key = f'spug:det:{det.id}'
        monitor_data = rds.hgetall(monitor_key)
        if monitor_data:
            print(f"✅ Redis中存储了监控状态: {len(monitor_data)} 个字段")
            for k, v in monitor_data.items():
                print(f"   {k.decode()}: {v.decode()}")
        else:
            print(f"⚠️ Redis中没有找到监控状态")
            
    except Exception as e:
        print(f"❌ 监控任务执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    run_correct_monitor()
