import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Button, Upload, message, Space, Spin, Progress, Tag, Tooltip } from 'antd';
import { UploadOutlined, BarChartOutlined, DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import { useHistory } from 'react-router-dom';
import * as VTable from '@visactor/vtable';

// 智能提取模板 - 基于您提供的固定格式，支持多个空格
const LOG_EXTRACTION_TEMPLATE = {
  patterns: [
    { key: 'successful_requests', pattern: /Successful requests:\s+([\d,]+)/, type: 'number' },
    { key: 'benchmark_duration', pattern: /Benchmark duration \(s\):\s+([\d.]+)/, type: 'number' },
    { key: 'total_input_tokens', pattern: /Total input tokens:\s+([\d,]+)/, type: 'number' },
    { key: 'total_generated_tokens', pattern: /Total generated tokens:\s+([\d,]+)/, type: 'number' },
    { key: 'request_throughput', pattern: /Request throughput \(req\/s\):\s+([\d.]+)/, type: 'number' },
    { key: 'output_token_throughput', pattern: /Output token throughput \(tok\/s\):\s+([\d.]+)/, type: 'number' },
    { key: 'total_token_throughput', pattern: /Total Token throughput \(tok\/s\):\s+([\d.]+)/, type: 'number' },
    { key: 'mean_ttft', pattern: /Mean TTFT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'median_ttft', pattern: /Median TTFT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'p99_ttft', pattern: /P99 TTFT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'mean_tpot', pattern: /Mean TPOT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'median_tpot', pattern: /Median TPOT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'p99_tpot', pattern: /P99 TPOT \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'mean_itl', pattern: /Mean ITL \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'median_itl', pattern: /Median ITL \(ms\):\s+([\d.]+)/, type: 'number' },
    { key: 'p99_itl', pattern: /P99 ITL \(ms\):\s+([\d.]+)/, type: 'number' }
  ]
};

// VTable 列配置
const TABLE_COLUMNS = [
  {
    field: 'filename',
    title: '文件名',
    width: 200,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f2f5' },
    style: { textAlign: 'left' }
  },
  {
    field: 'successful_requests',
    title: '成功请求数',
    width: 120,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#e6f7ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  },
  {
    field: 'benchmark_duration',
    title: '基准时长(s)',
    width: 120,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f6ffed' },
    style: { textAlign: 'center' }
  },
  {
    field: 'total_input_tokens',
    title: '输入Token总数',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2e8' },
    style: { textAlign: 'center' }
  },
  {
    field: 'total_generated_tokens',
    title: '生成Token总数',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff2e8' },
    style: { textAlign: 'center' }
  },
  {
    field: 'request_throughput',
    title: '请求吞吐量(req/s)',
    width: 150,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' }
  },
  {
    field: 'output_token_throughput',
    title: '输出Token吞吐量(tok/s)',
    width: 180,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' }
  },
  {
    field: 'total_token_throughput',
    title: '总Token吞吐量(tok/s)',
    width: 170,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f9f0ff' },
    style: { textAlign: 'center', color: '#722ed1' }
  },
  {
    field: 'mean_ttft',
    title: '平均TTFT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff1f0' },
    style: { textAlign: 'center', color: '#cf1322' }
  },
  {
    field: 'median_ttft',
    title: '中位TTFT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff1f0' },
    style: { textAlign: 'center', color: '#cf1322' }
  },
  {
    field: 'p99_ttft',
    title: 'P99 TTFT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#fff1f0' },
    style: { textAlign: 'center', color: '#cf1322' }
  },
  {
    field: 'mean_tpot',
    title: '平均TPOT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#feffe6' },
    style: { textAlign: 'center', color: '#d48806' }
  },
  {
    field: 'median_tpot',
    title: '中位TPOT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#feffe6' },
    style: { textAlign: 'center', color: '#d48806' }
  },
  {
    field: 'p99_tpot',
    title: 'P99 TPOT(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#feffe6' },
    style: { textAlign: 'center', color: '#d48806' }
  },
  {
    field: 'mean_itl',
    title: '平均ITL(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  },
  {
    field: 'median_itl',
    title: '中位ITL(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  },
  {
    field: 'p99_itl',
    title: 'P99 ITL(ms)',
    width: 130,
    sort: true,
    headerStyle: { fontWeight: 'bold', bgColor: '#f0f5ff' },
    style: { textAlign: 'center', color: '#1890ff' }
  }
];

const LogAnalysisTable = ({ selectedFiles, hostId, containerName }) => {
  const [loading, setLoading] = useState(false);
  const [extractedData, setExtractedData] = useState([]);
  const [progress, setProgress] = useState(0);
  const [tableInstance, setTableInstance] = useState(null);
  const containerRef = useRef(null);
  const history = useHistory();

  // 处理后端解析的数据（不再需要前端解析）
  const processBackendData = useCallback((parsedData, filename) => {
    console.log('[DEBUG] processBackendData 开始处理，文件名:', filename);
    console.log('[DEBUG] parsedData 类型:', typeof parsedData);
    console.log('[DEBUG] parsedData 内容:', parsedData);
    console.log('[DEBUG] parsedData 是否为null:', parsedData === null);
    console.log('[DEBUG] parsedData 是否为undefined:', parsedData === undefined);

    // 如果后端已经解析了数据，直接使用
    if (parsedData && typeof parsedData === 'object') {
      console.log('[DEBUG] 使用后端解析的数据:', parsedData);
      return parsedData;
    }

    // 如果后端没有解析数据，返回默认结构
    console.log('[DEBUG] 后端没有解析数据，创建默认结构');
    const defaultResult = { filename };
    LOG_EXTRACTION_TEMPLATE.patterns.forEach(({ key, type }) => {
      defaultResult[key] = type === 'number' ? 0 : '-';
    });

    console.log('[DEBUG] 使用默认结构:', defaultResult);
    return defaultResult;
  }, []);

  // 跳转到新的VTable页面进行分析
  const handleAnalyzeInNewPage = () => {
    if (!selectedFiles || selectedFiles.length === 0) {
      message.warning('请先选择要分析的日志文件');
      return;
    }

    // 将分析参数存储到sessionStorage，供新页面使用
    const analysisParams = {
      selectedFiles,
      hostId,
      containerName,
      timestamp: Date.now()
    };

    sessionStorage.setItem('logAnalysisParams', JSON.stringify(analysisParams));

    // 跳转到新的VTable分析页面
    history.push('/exec/result-extraction/vtable-analysis');
  };

  // 获取文件内容并提取数据
  const processFiles = useCallback(async () => {
    if (!selectedFiles || selectedFiles.length === 0) {
      message.warning('请先选择要分析的日志文件');
      return;
    }

    setLoading(true);
    setProgress(0);
    const results = [];

    try {
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        setProgress(Math.round(((i + 1) / selectedFiles.length) * 100));

        try {
          console.log(`[DEBUG] 开始处理文件: ${file.name}, 路径: ${file.fullPath}`);

          // 调用API获取文件内容和解析数据
          const apiUrl = `/api/exec/docker-file-content/?host_id=${hostId}&container_name=${containerName}&file_path=${encodeURIComponent(file.fullPath)}&token=1`;
          console.log(`[DEBUG] API请求URL: ${apiUrl}`);

          const response = await fetch(apiUrl);
          console.log(`[DEBUG] API响应状态: ${response.status}`);

          if (response.ok) {
            const responseData = await response.json();
            console.log(`[DEBUG] API响应数据:`, responseData);

            // 检查spug的标准响应格式: {data: {...}, error: ''}
            if (responseData.error) {
              console.warn(`[DEBUG] API返回错误: ${file.name}`, responseData.error);
              // 添加错误数据行
              results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '错误'])) });
            } else if (responseData.data) {
              const data = responseData.data;
              console.log(`[DEBUG] 文件 ${file.name} 获取成功，API数据:`, data);
              console.log(`[DEBUG] 文件 ${file.name} 的 success 字段:`, data.success);
              console.log(`[DEBUG] 文件 ${file.name} 的 parsed_data 字段:`, data.parsed_data);

              if (data.success) {
                // 使用后端解析的数据
                const processedData = processBackendData(data.parsed_data, file.name);
                console.log(`[DEBUG] 处理后的数据:`, processedData);

                results.push(processedData);
              } else {
                console.warn(`[DEBUG] 文件处理失败: ${file.name}`, data.error || '未知错误');
                // 添加空数据行以保持文件记录
                results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '-'])) });
              }
            } else {
              console.warn(`[DEBUG] API响应格式异常: ${file.name}`, responseData);
              // 添加空数据行以保持文件记录
              results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '格式错误'])) });
            }
          } else {
            console.error(`[DEBUG] API请求失败: ${file.name}`, response.status);
            results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '网络错误'])) });
          }
        } catch (error) {
          console.error(`[DEBUG] 处理文件 ${file.name} 时出错:`, error);
          // 添加错误数据行
          results.push({ filename: file.name, ...Object.fromEntries(LOG_EXTRACTION_TEMPLATE.patterns.map(p => [p.key, p.type === 'number' ? 0 : '错误'])) });
        }
      }

      setExtractedData(results);
      message.success(`成功分析了 ${results.length} 个日志文件`);
    } catch (error) {
      console.error('处理文件时出错:', error);
      message.error('处理文件时出错');
    } finally {
      setLoading(false);
      setProgress(0);
    }
  }, [selectedFiles, hostId, containerName, processBackendData]);

  // 初始化VTable
  useEffect(() => {
    if (containerRef.current && extractedData.length > 0) {
      // 清理现有表格
      if (tableInstance) {
        tableInstance.release();
      }

      const option = {
        container: containerRef.current,
        columns: TABLE_COLUMNS,
        records: extractedData,
        theme: VTable.themes.DEFAULT.extends({
          headerStyle: {
            borderColor: '#e8e8e8',
            borderLineWidth: 1
          },
          bodyStyle: {
            borderColor: '#f0f0f0',
            borderLineWidth: 1
          }
        }),
        widthMode: 'adaptive',
        heightMode: 'adaptive',
        autoWrapText: true,
        hover: {
          highlightMode: 'row'
        },
        select: {
          highlightMode: 'row'
        },
        menu: {
          contextMenuItems: ['复制', '导出选中行', '导出全部数据']
        }
      };

      const table = new VTable.ListTable(option);
      setTableInstance(table);

      // 监听菜单点击事件
      table.on('dropdown_menu_click', (args) => {
        console.log('菜单点击:', args);
        if (args.menuKey === '导出全部数据') {
          exportData();
        }
      });

      return () => {
        if (table) {
          table.release();
        }
      };
    }
  }, [extractedData]);

  // 导出数据
  const exportData = useCallback(() => {
    if (extractedData.length === 0) {
      message.warning('没有数据可导出');
      return;
    }

    const csvContent = [
      // CSV 头部
      TABLE_COLUMNS.map(col => col.title).join(','),
      // CSV 数据行
      ...extractedData.map(row => 
        TABLE_COLUMNS.map(col => row[col.field] || '').join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `log_analysis_${new Date().getTime()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    message.success('数据导出成功');
  }, [extractedData]);

  return (
    <Card 
      title={
        <Space>
          <BarChartOutlined />
          <span>日志性能分析表格</span>
          <Tag color="blue">{extractedData.length} 条记录</Tag>
        </Space>
      }
      extra={
        <Space>
          <Button
            type="primary"
            icon={<BarChartOutlined />}
            onClick={handleAnalyzeInNewPage}
            disabled={!selectedFiles || selectedFiles.length === 0}
          >
            数据并分析日志
          </Button>
          <Button 
            icon={<DownloadOutlined />}
            onClick={exportData}
            disabled={extractedData.length === 0}
          >
            导出数据
          </Button>
        </Space>
      }
      style={{ marginTop: 16 }}
    >
      {loading && (
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Progress percent={progress} status="active" />
            <p>正在分析日志文件... ({progress}%)</p>
          </div>
        </div>
      )}
      
      {!loading && extractedData.length === 0 && (
        <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
          <BarChartOutlined style={{ fontSize: 48, marginBottom: 16 }} />
          <p>请选择日志文件并点击"开始分析"按钮</p>
        </div>
      )}

      {!loading && extractedData.length > 0 && (
        <div 
          ref={containerRef} 
          style={{ 
            height: '600px', 
            width: '100%',
            border: '1px solid #d9d9d9',
            borderRadius: '6px'
          }} 
        />
      )}
    </Card>
  );
};

export default LogAnalysisTable;
