#!/usr/bin/env python3
"""
直接测试SSH连接
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.host.models import Host

def test_ssh_direct():
    """直接测试SSH连接"""
    print("=== 直接测试SSH连接 ===\n")
    
    # 获取主机
    host = Host.objects.get(pk=1)
    print(f"🖥️ 目标主机: {host.name} ({host.hostname})")
    
    try:
        with host.get_ssh() as ssh:
            # 测试基本命令
            print("测试基本命令...")
            exit_code, out = ssh.exec_command_raw("whoami")
            print(f"whoami: {out.strip()}")
            
            exit_code, out = ssh.exec_command_raw("pwd")
            print(f"pwd: {out.strip()}")
            
            # 测试文件存在性
            print("\n测试Docker文件...")
            exit_code, out = ssh.exec_command_raw("ls -la /usr/bin/docker")
            print(f"ls /usr/bin/docker (退出码: {exit_code}): {out.strip()}")
            
            # 测试执行权限
            exit_code, out = ssh.exec_command_raw("test -x /usr/bin/docker && echo 'executable' || echo 'not executable'")
            print(f"执行权限检查: {out.strip()}")
            
            # 尝试直接执行
            exit_code, out = ssh.exec_command_raw("/usr/bin/docker --version")
            print(f"docker --version (退出码: {exit_code}): {out.strip()}")
            
            # 检查Docker守护进程
            exit_code, out = ssh.exec_command_raw("systemctl is-active docker")
            print(f"Docker服务状态: {out.strip()}")
            
            # 尝试使用sudo
            exit_code, out = ssh.exec_command_raw("sudo /usr/bin/docker ps --format '{{.Names}}'")
            print(f"sudo docker ps (退出码: {exit_code}): {out.strip()}")
            
            if exit_code == 0 and out.strip():
                containers = [name.strip() for name in out.strip().split('\n') if name.strip()]
                print(f"发现容器: {containers}")
                
                # 测试容器中的进程搜索
                for container in containers:
                    print(f"\n测试容器 {container}:")
                    exit_code, out = ssh.exec_command_raw(f"sudo /usr/bin/docker exec {container} ps aux | grep run_28k_4k_qwen2 | grep -v grep")
                    print(f"进程搜索 (退出码: {exit_code}): {out.strip()}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_ssh_direct()
