#!/usr/bin/env python3
"""
检查远程服务器上实际运行的进程
"""
import os
import sys
import django

# 设置Django环境
sys.path.insert(0, '/'.join(os.path.abspath(__file__).split('/')[:-1]))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "spug.settings")
django.setup()

from apps.host.models import Host

def check_actual_processes():
    print("=== 检查远程服务器上的实际进程 ===\n")
    
    try:
        host = Host.objects.get(id=5)
        print(f"连接主机: {host.name} ({host.hostname})")
        
        with host.get_ssh() as ssh:
            # 查看所有包含qwen的进程
            commands = [
                "ps aux | grep qwen | grep -v grep",
                "ps aux | grep 28k | grep -v grep", 
                "ps aux | grep bash | grep -v grep | head -10",
                "docker ps",
                "docker exec Qwen3 ps aux | grep qwen | grep -v grep"
            ]
            
            for i, command in enumerate(commands):
                print(f"\n🔍 执行命令 {i+1}: {command}")
                print("=" * 60)
                
                try:
                    exit_code, out = ssh.exec_command_raw(command)
                    print(f"退出码: {exit_code}")
                    if out.strip():
                        print("输出:")
                        print(out)
                    else:
                        print("无输出")
                except Exception as e:
                    print(f"❌ 命令执行失败: {e}")
                
                print("=" * 60)
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    check_actual_processes()
