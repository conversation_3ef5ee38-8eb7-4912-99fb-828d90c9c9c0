#!/usr/bin/env python3
"""
带调试的API测试
"""
import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.models import Detection
from django_redis import get_redis_connection
from datetime import datetime

# 临时修改API函数以添加调试信息
def debug_get_overview():
    """带调试的get_overview函数"""
    print("=== 调试get_overview函数 ===\n")
    
    response = []
    rds = get_redis_connection()
    
    print("📊 开始处理监控任务...")
    all_detections = Detection.objects.all()
    print(f"数据库中共有 {all_detections.count()} 个监控任务")
    
    for item in all_detections:
        print(f"\n🔍 处理监控任务: {item.name} (ID: {item.id})")
        print(f"   是否激活: {item.is_active}")
        print(f"   目标: {item.targets}")
        
        data = {}
        
        try:
            targets = json.loads(item.targets)
            print(f"   解析后的目标: {targets}")
        except Exception as e:
            print(f"   ❌ 目标解析失败: {e}")
            continue
        
        for key in targets:
            key = str(key)
            print(f"     处理目标: {key}")
            
            data[key] = {
                'id': f'{item.id}_{key}',
                'group': item.group,
                'name': item.name,
                'type': item.get_type_display(),
                'target': key,
                'desc': item.desc,
                'status': '0',
                'latest_run_time': item.latest_run_time,
                'process_info': None,
            }
            
            # 获取进程详细信息（仅对智能进程检测）
            if item.type == '6':
                process_info_key = f'spug:det:info:{item.id}_{key}'
                process_info_data = rds.get(process_info_key)
                if process_info_data:
                    try:
                        data[key]['process_info'] = json.loads(process_info_data.decode())
                        print(f"       ✅ 找到进程信息: {len(data[key]['process_info'])} 个进程")
                    except:
                        print(f"       ❌ 进程信息解析失败")
                else:
                    print(f"       ⚠️ 没有找到进程信息")

            if item.is_active:
                if item.latest_run_time:
                    data[key]['status'] = '1'
                    print(f"       状态设置为: 正常 (1)")
                else:
                    data[key]['status'] = '10'
                    print(f"       状态设置为: 未运行 (10)")
        
        print(f"   当前data: {len(data)} 个目标")
        
        if item.is_active:
            print(f"   监控任务已激活，检查Redis状态...")
            monitor_key = f'spug:det:{item.id}'
            monitor_data = rds.hgetall(monitor_key)
            print(f"   Redis状态数据: {len(monitor_data)} 个字段")
            
            for key, val in monitor_data.items():
                prefix, key = key.decode().split('_', 1)
                if key in data:
                    val = int(val)
                    if prefix == 'c':
                        if data[key]['status'] == '1':
                            data[key]['status'] = '2'
                        data[key]['count'] = val
                        print(f"       更新目标 {key} 状态为警告")
                    elif prefix == 't':
                        date = datetime.fromtimestamp(val).strftime('%Y-%m-%d %H:%M:%S')
                        data[key].update(status='3', notified_at=date)
                        print(f"       更新目标 {key} 状态为故障")
            
            data_values = list(data.values())
            response.extend(data_values)
            print(f"   ✅ 添加了 {len(data_values)} 个监控项到响应")
        else:
            print(f"   ❌ 监控任务未激活，跳过")
    
    print(f"\n📋 最终响应: {len(response)} 个监控项")
    return response

def test_api_with_debug():
    """测试带调试的API"""
    print("=== 测试带调试的API ===\n")
    
    # 调用调试版本的函数
    result = debug_get_overview()
    
    print(f"\n🔍 调试版本结果: {len(result)} 个监控项")
    for item in result:
        print(f"  - {item['name']}: 状态={item['status']}, 进程信息={'有' if item['process_info'] else '无'}")
    
    # 调用实际的API函数
    print(f"\n🌐 测试实际API函数...")
    from apps.monitor.views import get_overview
    from django.test import RequestFactory
    from apps.account.models import User
    
    factory = RequestFactory()
    request = factory.get('/api/monitor/overview/')
    request.user = User.objects.first()
    
    try:
        response_obj = get_overview(request)
        if hasattr(response_obj, 'content'):
            content = response_obj.content.decode()
            response_data = json.loads(content)
            
            print(f"API响应结构: {list(response_data.keys())}")
            if 'data' in response_data:
                api_data = response_data['data']
                print(f"API返回数据: {len(api_data)} 个监控项")
                
                for item in api_data:
                    print(f"  - {item.get('name')}: 状态={item.get('status')}, 进程信息={'有' if item.get('process_info') else '无'}")
            else:
                print(f"API响应格式错误: {response_data}")
        else:
            print(f"API调用失败")
    except Exception as e:
        print(f"API调用异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_api_with_debug()
