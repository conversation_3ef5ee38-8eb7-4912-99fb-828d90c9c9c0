#!/usr/bin/env python3
"""
检查用户权限
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.account.models import User

def check_user_permissions():
    """检查用户权限"""
    print("=== 检查用户权限 ===\n")
    
    # 获取用户
    user = User.objects.first()
    if not user:
        print("❌ 没有找到用户")
        return
    
    print(f"👤 用户信息:")
    print(f"   用户名: {user.username}")
    print(f"   是否超级用户: {user.is_supper}")
    print(f"   是否激活: {user.is_active}")
    
    # 检查权限
    print(f"\n🔐 权限检查:")
    
    # 检查monitor.monitor.view权限
    monitor_perms = ['monitor.monitor.view', 'monitor.monitor.add', 'monitor.monitor.edit']
    
    for perm in monitor_perms:
        has_perm = user.has_perms([perm])
        print(f"   {perm}: {'✅ 有权限' if has_perm else '❌ 无权限'}")
    
    # 如果是超级用户，应该有所有权限
    if user.is_supper:
        print(f"   ✅ 超级用户，拥有所有权限")
    else:
        print(f"   ⚠️ 非超级用户，需要检查具体权限")
        
        # 获取用户的所有权限
        print(f"\n📋 用户的所有权限:")
        if hasattr(user, 'role'):
            print(f"   角色: {user.role}")
            if hasattr(user.role, 'perms'):
                perms = user.role.perms
                print(f"   权限列表: {perms}")
        else:
            print(f"   用户没有关联角色")
    
    # 测试权限检查函数
    print(f"\n🧪 测试权限检查函数:")
    
    # 测试单个权限
    result = user.has_perms(['monitor.monitor.view'])
    print(f"   has_perms(['monitor.monitor.view']): {result}")
    
    # 测试多个权限（OR）
    result = user.has_perms(['monitor.monitor.view', 'monitor.monitor.add'])
    print(f"   has_perms(['monitor.monitor.view', 'monitor.monitor.add']): {result}")
    
    # 如果用户没有权限，尝试给用户添加权限
    if not user.has_perms(['monitor.monitor.view']):
        print(f"\n🔧 用户没有monitor.monitor.view权限，尝试添加...")
        
        # 方法1：设置为超级用户
        print(f"   方法1：设置为超级用户")
        user.is_supper = True
        user.save()
        print(f"   ✅ 已设置为超级用户")
        
        # 重新测试权限
        result = user.has_perms(['monitor.monitor.view'])
        print(f"   重新测试权限: {result}")

if __name__ == '__main__':
    check_user_permissions()
