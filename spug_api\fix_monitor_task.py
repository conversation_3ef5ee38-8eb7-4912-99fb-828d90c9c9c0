#!/usr/bin/env python3
"""
修复监控任务配置
"""
import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.models import Detection
from apps.host.models import Host

def fix_monitor_task():
    """修复监控任务配置"""
    print("=== 修复监控任务配置 ===\n")
    
    # 获取现有的监控任务
    det = Detection.objects.filter(name='run_28k_4k_qwen2').first()
    if not det:
        print("❌ 没有找到监控任务")
        return
    
    print(f"📋 当前监控任务:")
    print(f"   ID: {det.id}")
    print(f"   名称: {det.name}")
    print(f"   目标: {det.targets}")
    print(f"   类型: {det.type}")
    print(f"   是否激活: {det.is_active}")
    
    # 获取正确的主机ID
    host = Host.objects.filter(hostname='**********').first()
    if not host:
        print("❌ 没有找到目标主机")
        return
    
    print(f"\n🖥️ 目标主机:")
    print(f"   ID: {host.id}")
    print(f"   名称: {host.name}")
    print(f"   主机名: {host.hostname}")
    
    # 更新监控任务的目标
    current_targets = json.loads(det.targets)
    print(f"\n🔧 当前目标: {current_targets}")
    
    new_targets = [host.id]
    det.targets = json.dumps(new_targets)
    det.is_active = True
    det.save()
    
    print(f"✅ 更新目标为: {new_targets}")
    print(f"✅ 监控任务已激活")
    
    # 运行一次监控任务测试
    print(f"\n🔄 运行监控任务测试...")
    from apps.monitor.executors import monitor_worker_handler
    
    job_data = [det.id, '6', host.id, 'run_28k_4k_qwen2.sh|container_first', 3, 24*60]
    job_json = json.dumps(job_data)
    
    try:
        monitor_worker_handler(job_json)
        print("✅ 监控任务执行成功")
        
        # 检查Redis中的数据
        from django_redis import get_redis_connection
        rds = get_redis_connection()
        
        process_info_key = f'spug:det:info:{det.id}_{host.id}'
        process_info_data = rds.get(process_info_key)
        
        if process_info_data:
            try:
                process_info = json.loads(process_info_data.decode())
                print(f"✅ Redis中存储了进程信息: {len(process_info)} 个进程")
                for i, proc in enumerate(process_info[:2]):
                    print(f"   进程 {i+1}: PID={proc.get('pid')}, 运行时间={proc.get('etime')}")
            except Exception as e:
                print(f"❌ 解析Redis数据失败: {e}")
        else:
            print(f"⚠️ Redis中没有找到进程信息")
            
    except Exception as e:
        print(f"❌ 监控任务执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    fix_monitor_task()
