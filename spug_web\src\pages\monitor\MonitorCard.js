/**
 * Copyright (c) OpenSpug Organization. https://github.com/openspug/spug
 * Copyright (c) <<EMAIL>>
 * Released under the AGPL-3.0 License.
 */
import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import { Card, Input, Select, Space, Tooltip, Spin, message } from 'antd';
import { FrownOutlined, ReloadOutlined, SyncOutlined } from '@ant-design/icons';
import styles from './index.module.less';
import store from './store';

const StyleMap = {
  '0': {background: '#99999933', border: '1px solid #999', color: '#999999'},
  '1': {background: '#16a98733', border: '1px solid #16a987', color: '#16a987'},
  '2': {background: '#ffba0033', border: '1px solid #ffba00', color: '#ffba00'},
  '3': {background: '#f2655d33', border: '1px solid #f2655d', color: '#f2655d'},
  '10': {background: '#99999919', border: '1px dashed #999999', color: '#999999'}
}

const StatusMap = {
  '1': '正常',
  '2': '警告',
  '3': '紧急',
  '0': '未激活',
  '10': '待调度'
}

function CardItem(props) {
  const {status, type, group, desc, name, target, latest_run_time, process_info} = props.data

  // 构建详细信息悬浮提示
  const baseInfo = [
    <div key="group">分组: {group}</div>,
    <div key="type">类型: {type}</div>,
    <div key="name">名称: {name}</div>,
    <div key="target">目标: {target}</div>,
    <div key="status">状态: {StatusMap[status]}</div>,
    <div key="update">更新: {latest_run_time || '---'}</div>,
    <div key="desc">描述: {desc}</div>
  ];

  // 如果是智能进程检测且有进程信息，添加进程详情
  if (type === '智能进程检测' && process_info && process_info.length > 0) {
    baseInfo.push(<div key="separator" style={{borderTop: '1px solid #ddd', margin: '8px 0'}}></div>);
    baseInfo.push(<div key="process-title" style={{fontWeight: 'bold'}}>进程详情:</div>);

    process_info.slice(0, 3).forEach((proc, index) => {
      baseInfo.push(
        <div key={`process-${index}`} style={{marginLeft: '8px', fontSize: '12px'}}>
          <div>进程 {index + 1}:</div>
          <div>  PID: {proc.pid}</div>
          <div>  命令: {proc.cmd}</div>
          <div>  运行时间: {proc.etime}</div>
          <div>  位置: {proc.location}</div>
        </div>
      );
    });

    if (process_info.length > 3) {
      baseInfo.push(<div key="more" style={{marginLeft: '8px', fontSize: '12px', color: '#999'}}>... 还有 {process_info.length - 3} 个进程</div>);
    }
  }

  const title = <div>{baseInfo}</div>;

  // 正确的状态判断逻辑：有进程信息且进程数量大于0表示运行中
  const hasRunningProcess = process_info && process_info.length > 0;
  const isRunning = hasRunningProcess && (status === '1' || status === '2');

  // 格式化运行时间显示
  function formatRuntime(etime) {
    if (!etime || etime === '0') return '未运行';

    // 处理格式如 "2-15:30:45" (天-小时:分钟:秒) - 先处理这个格式
    if (etime.includes('-') && etime.includes(':')) {
      const [days, time] = etime.split('-');
      const [hours, minutes] = time.split(':');

      const dayNum = parseInt(days);
      const hourNum = parseInt(hours);
      const minNum = parseInt(minutes);

      if (dayNum > 0) {
        return `${dayNum}天${hourNum}小时`;
      } else if (hourNum > 0) {
        return `${hourNum}小时${minNum}分钟`;
      } else {
        return `${minNum}分钟`;
      }
    }

    // 处理格式如 "05:04:25" (小时:分钟:秒)
    if (etime.includes(':') && !etime.includes('-')) {
      const parts = etime.split(':');
      if (parts.length === 3) {
        const hours = parseInt(parts[0]);
        const minutes = parseInt(parts[1]);

        if (hours > 0) {
          return `${hours}小时${minutes}分钟`;
        } else if (minutes > 0) {
          return `${minutes}分钟`;
        } else {
          return '刚启动';
        }
      }
    }

    return etime;
  }

  // 获取运行时间
  let displayRuntime = '未运行';
  if (hasRunningProcess) {
    const runtimes = process_info.map(proc => proc.etime || '0').filter(time => time !== '0');
    if (runtimes.length > 0) {
      displayRuntime = formatRuntime(runtimes[0]);
    }
  }

  return (
    <Tooltip title={title} placement="top">
      <div className={`${styles.processCard} ${isRunning ? styles.running : styles.stopped}`}>
        {/* 进程名称 */}
        <div className={styles.processTitle}>
          {name}
        </div>

        {/* 状态和运行时间 */}
        <div className={styles.processStatus}>
          <div className={styles.statusRow}>
            <span className={`${styles.statusDot} ${isRunning ? styles.running : styles.stopped}`}></span>
            <span className={styles.statusText}>
              {isRunning ? '运行中' : '未运行'}
            </span>
          </div>

          <div className={styles.runtimeRow}>
            <span className={styles.runtimeText}>
              {displayRuntime}
            </span>
          </div>
        </div>
      </div>
    </Tooltip>
  )
}

function MonitorCard() {
  const [autoReload, setAutoReload] = useState(false);
  const [status, setStatus] = useState();

  useEffect(() => {
    store.fetchOverviews()

    return () => store.autoReload = null
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  function handleAutoReload() {
    store.autoReload = !autoReload
    message.info(autoReload ? '关闭自动刷新' : '开启自动刷新')
    if (!autoReload) store.fetchOverviews()
    setAutoReload(!autoReload)
  }

  const filteredRecords = store.ovDataSource.filter(x => !status || x.status === status)
  return (
    <Card title="总览" style={{marginBottom: 24}} bodyStyle={{padding: '12px 24px'}} extra={(
      <Space size="middle">
        <Space>
          <div>分组：</div>
          <Select allowClear style={{minWidth: 150}} value={store.f_group} onChange={v => store.f_group = v}
                  placeholder="请选择">
            {store.groups.map(item => (
              <Select.Option value={item} key={item}>{item}</Select.Option>
            ))}
          </Select>
        </Space>
        <Space>
          <div>类型：</div>
          <Select allowClear style={{width: 120}} value={store.f_type} onChange={v => store.f_type = v}
                  placeholder="请选择">
            {store.types.map(item => <Select.Option key={item} value={item}>{item}</Select.Option>)}
          </Select>
        </Space>
        <Space>
          <div>名称：</div>
          <Input allowClear value={store.f_name} onChange={e => store.f_name = e.target.value} placeholder="请输入"/>
        </Space>
      </Space>
    )}>
      <Spin spinning={store.ovFetching}>
        <div className={styles.header}>
          {Object.entries(StyleMap).map(([s, style]) => {
            const count = store.ovDataSource.filter(x => x.status === s).length;
            return count ? (
              <Tooltip key={s} title={StatusMap[s]}>
                <div
                  className={styles.item}
                  style={s === status ? style : {...style, background: '#fff'}}
                  onClick={() => setStatus(s === status ? '' : s)}>
                  {store.ovDataSource.filter(x => x.status === s).length}
                </div>
              </Tooltip>
            ) : null
          })}
          <Tooltip title="自动刷新">
            <div className={styles.autoLoad} onClick={handleAutoReload}>
              {autoReload ? <SyncOutlined spin style={{color: '#2563fc'}}/> : <ReloadOutlined/>}
            </div>
          </Tooltip>
        </div>
        {filteredRecords.length > 0 ? (
          <div className={styles.cardGrid}>
            {filteredRecords.map(item => (
              <CardItem key={item.id} data={item}/>
            ))}
          </div>
        ) : (
          <div className={styles.notMatch}><FrownOutlined/>暂无匹配的监控项</div>
        )}
      </Spin>
    </Card>
  )
}

export default observer(MonitorCard)