#!/usr/bin/env python3
"""
检查Redis中存储的进程信息格式
"""
import os
import sys
import json
import django

# 设置Django环境
sys.path.insert(0, '/'.join(os.path.abspath(__file__).split('/')[:-1]))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "spug.settings")
django.setup()

from django_redis import get_redis_connection

def check_redis_process_info():
    print("=== 检查Redis中的进程信息 ===\n")
    
    rds = get_redis_connection()
    
    # 查找所有进程信息键
    process_keys = rds.keys('spug:det:info:*')
    
    if not process_keys:
        print("❌ Redis中没有找到任何进程信息")
        return
    
    print(f"📋 找到 {len(process_keys)} 个进程信息键:")
    
    for key in process_keys:
        key_str = key.decode() if isinstance(key, bytes) else key
        print(f"\n🔍 键: {key_str}")
        
        try:
            data = rds.get(key)
            if data:
                process_info = json.loads(data.decode())
                print(f"   进程数量: {len(process_info)}")
                
                for i, proc in enumerate(process_info):
                    print(f"   进程 {i+1}:")
                    print(f"     PID: {proc.get('pid', 'N/A')}")
                    print(f"     命令: {proc.get('cmd', 'N/A')}")
                    print(f"     运行时间: {proc.get('etime', 'N/A')}")
                    print(f"     位置: {proc.get('location', 'N/A')}")
                    print(f"     CPU: {proc.get('pcpu', 'N/A')}")
                    print(f"     内存: {proc.get('pmem', 'N/A')}")
            else:
                print("   ❌ 数据为空")
        except Exception as e:
            print(f"   ❌ 解析失败: {e}")

if __name__ == '__main__':
    check_redis_process_info()
