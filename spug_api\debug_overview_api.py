#!/usr/bin/env python3
"""
调试监控总览API
"""
import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.models import Detection
from apps.monitor.views import get_overview
from django.test import RequestFactory
from apps.account.models import User
from django_redis import get_redis_connection

def debug_overview_api():
    """调试监控总览API"""
    print("=== 调试监控总览API ===\n")
    
    # 检查监控任务
    print("📊 检查监控任务...")
    detections = Detection.objects.all()
    print(f"数据库中有 {detections.count()} 个监控任务:")
    
    for det in detections:
        print(f"  - ID: {det.id}")
        print(f"    名称: {det.name}")
        print(f"    类型: {det.type} ({det.get_type_display()})")
        print(f"    目标: {det.targets}")
        print(f"    是否激活: {det.is_active}")
        print(f"    最后运行: {det.latest_run_time}")
        
        # 解析目标
        try:
            targets = json.loads(det.targets)
            print(f"    解析后的目标: {targets}")
        except:
            print(f"    目标解析失败")
        print()
    
    # 手动模拟API逻辑
    print("🔍 手动模拟API逻辑...")
    response = []
    rds = get_redis_connection()
    
    for item in Detection.objects.all():
        print(f"\n处理监控任务: {item.name}")
        data = {}
        
        try:
            targets = json.loads(item.targets)
            print(f"  目标列表: {targets}")
        except Exception as e:
            print(f"  目标解析失败: {e}")
            continue
            
        for key in targets:
            key = str(key)
            print(f"  处理目标: {key}")
            
            data[key] = {
                'id': f'{item.id}_{key}',
                'group': item.group,
                'name': item.name,
                'type': item.get_type_display(),
                'target': key,
                'desc': item.desc,
                'status': '0',
                'latest_run_time': item.latest_run_time,
                'process_info': None,
            }
            
            # 获取进程详细信息（仅对智能进程检测）
            if item.type == '6':
                process_info_key = f'spug:det:info:{item.id}_{key}'
                print(f"    查找进程信息: {process_info_key}")
                
                process_info_data = rds.get(process_info_key)
                if process_info_data:
                    try:
                        process_info = json.loads(process_info_data.decode())
                        data[key]['process_info'] = process_info
                        print(f"    ✅ 找到进程信息: {len(process_info)} 个进程")
                    except Exception as e:
                        print(f"    ❌ 进程信息解析失败: {e}")
                else:
                    print(f"    ⚠️ 没有找到进程信息")
            
            if item.is_active:
                if item.latest_run_time:
                    data[key]['status'] = '1'
                else:
                    data[key]['status'] = '10'
        
        if item.is_active:
            monitor_key = f'spug:det:{item.id}'
            print(f"  检查监控状态: {monitor_key}")
            
            monitor_data = rds.hgetall(monitor_key)
            if monitor_data:
                print(f"    监控状态数据: {len(monitor_data)} 个字段")
                for key, val in monitor_data.items():
                    prefix, target_key = key.decode().split('_', 1)
                    if target_key in data:
                        val = int(val)
                        if prefix == 'c':
                            if data[target_key]['status'] == '1':
                                data[target_key]['status'] = '2'
                            data[target_key]['count'] = val
                        elif prefix == 't':
                            from datetime import datetime
                            date = datetime.fromtimestamp(val).strftime('%Y-%m-%d %H:%M:%S')
                            data[target_key].update(status='3', notified_at=date)
            else:
                print(f"    没有监控状态数据")
        
        response.extend(list(data.values()))
    
    print(f"\n📋 最终响应数据: {len(response)} 个监控项")
    for item in response:
        print(f"  - {item['name']}: 状态={item['status']}, 进程信息={'有' if item['process_info'] else '无'}")

if __name__ == '__main__':
    debug_overview_api()
