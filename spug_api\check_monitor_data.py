#!/usr/bin/env python3
"""
检查监控数据
"""
import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.models import Detection
from apps.host.models import Host
from django_redis import get_redis_connection

def check_monitor_data():
    """检查监控数据"""
    print("=== 检查监控数据 ===\n")
    
    # 检查监控任务
    print("📊 检查监控任务...")
    detections = Detection.objects.all()
    print(f"发现 {detections.count()} 个监控任务:")
    
    for det in detections:
        print(f"  - ID: {det.id}")
        print(f"    名称: {det.name}")
        print(f"    类型: {det.type} ({det.get_type_display()})")
        print(f"    目标: {det.targets}")
        print(f"    是否激活: {det.is_active}")
        print(f"    最后运行: {det.latest_run_time}")
        print()
    
    # 检查主机
    print("🖥️ 检查主机...")
    hosts = Host.objects.filter(is_verified=True)
    print(f"发现 {hosts.count()} 个已验证主机:")
    
    for host in hosts:
        print(f"  - ID: {host.id}")
        print(f"    名称: {host.name}")
        print(f"    主机名: {host.hostname}")
        print()
    
    # 检查Redis数据
    print("🗄️ 检查Redis数据...")
    rds = get_redis_connection()
    
    # 查找所有监控相关的key
    monitor_keys = rds.keys('spug:det:*')
    print(f"发现 {len(monitor_keys)} 个监控相关的Redis key:")
    
    for key in monitor_keys:
        key_str = key.decode() if isinstance(key, bytes) else key
        print(f"  - {key_str}")
        
        if 'info' in key_str:
            # 这是进程信息
            data = rds.get(key)
            if data:
                try:
                    process_info = json.loads(data.decode())
                    print(f"    进程信息: {len(process_info)} 个进程")
                except:
                    print(f"    数据解析失败")
        else:
            # 这是监控状态
            data = rds.hgetall(key)
            if data:
                print(f"    状态数据: {len(data)} 个字段")
                for k, v in data.items():
                    print(f"      {k.decode()}: {v.decode()}")
    
    # 如果没有监控任务，创建一个测试任务
    if detections.count() == 0:
        print("\n🔧 没有监控任务，创建测试任务...")
        host = hosts.first()
        if host:
            det = Detection.objects.create(
                name='run_28k_4k_qwen2',
                type='6',  # 智能进程检测
                targets=json.dumps([host.id]),
                extra='run_28k_4k_qwen2.sh|container_first',
                rate=5,
                threshold=3,
                is_active=True,
                notify_grp='[]',
                notify_mode='["4"]',
                desc='智能进程检测测试'
            )
            print(f"✅ 创建了测试监控任务: ID={det.id}")
        else:
            print("❌ 没有可用主机，无法创建监控任务")

if __name__ == '__main__':
    check_monitor_data()
