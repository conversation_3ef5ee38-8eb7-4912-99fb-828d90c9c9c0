/**
 * 配置日志获取参数组件 - 用于智能结果收集器中的多文件提取
 */
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Select,
  Input,
  Switch,
  Button,
  Space,
  message,
  Alert,
  Divider,
  Tag,
  Tooltip,
  List,
  Checkbox,
  Spin,
  Empty,
  Card,
  AutoComplete
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  CloudServerOutlined,
  FileTextOutlined,
  SettingOutlined,
  FolderOutlined,
  EyeOutlined,
  ReloadOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { http } from 'libs';
import styles from './LogExtractionConfig.module.less';
import LogAnalysisTable from './LogAnalysisTable';

const { Option } = Select;
const { TextArea } = Input;

function LogExtractionConfig({ visible, onCancel, onSuccess, initialLogPath = '', executionInfo = {} }) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [hosts, setHosts] = useState([]);
  const [useDocker, setUseDocker] = useState(false);
  const [extractionMode, setExtractionMode] = useState('multiple');
  const [logPaths, setLogPaths] = useState([initialLogPath].filter(Boolean));
  const [folderPath, setFolderPath] = useState('');
  const [fileList, setFileList] = useState([]);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [loadingFiles, setLoadingFiles] = useState(false);
  const [selectAll, setSelectAll] = useState(false);
  const [previewFile, setPreviewFile] = useState(null);
  const [previewContent, setPreviewContent] = useState('');
  const [previewLoading, setPreviewLoading] = useState(false);
  const [dockerContainers, setDockerContainers] = useState([]);
  const [loadingContainers, setLoadingContainers] = useState(false);
  const [pathSuggestions, setPathSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [recentPaths, setRecentPaths] = useState([]);

  // 最近使用目录管理
  const RECENT_PATHS_KEY = 'log_extraction_recent_paths';
  const MAX_RECENT_PATHS = 2;

  // 加载最近使用的目录
  const loadRecentPaths = () => {
    try {
      const saved = localStorage.getItem(RECENT_PATHS_KEY);
      if (saved) {
        const paths = JSON.parse(saved);
        setRecentPaths(Array.isArray(paths) ? paths.slice(0, MAX_RECENT_PATHS) : []);
      }
    } catch (error) {
      console.warn('加载最近使用目录失败:', error);
      setRecentPaths([]);
    }
  };

  // 保存最近使用的目录
  const saveRecentPath = (path) => {
    if (!path || !path.trim()) return;

    try {
      const trimmedPath = path.trim();
      let updatedPaths = [...recentPaths];

      // 移除已存在的相同路径
      updatedPaths = updatedPaths.filter(p => p !== trimmedPath);

      // 添加到开头
      updatedPaths.unshift(trimmedPath);

      // 限制最大数量
      updatedPaths = updatedPaths.slice(0, MAX_RECENT_PATHS);

      setRecentPaths(updatedPaths);
      localStorage.setItem(RECENT_PATHS_KEY, JSON.stringify(updatedPaths));
    } catch (error) {
      console.warn('保存最近使用目录失败:', error);
    }
  };

  // 清除最近使用目录
  const clearRecentPaths = () => {
    setRecentPaths([]);
    localStorage.removeItem(RECENT_PATHS_KEY);
  };

  useEffect(() => {
    if (visible) {
      console.log('[DEBUG] LogExtractionConfig打开，executionInfo:', executionInfo);
      fetchHosts();
      loadRecentPaths(); // 加载最近使用的目录

      // 处理从URL传来的参数
      if (executionInfo.hostId) {
        console.log('[DEBUG] 设置主机ID:', executionInfo.hostId);
        const hostId = parseInt(executionInfo.hostId);
        form.setFieldsValue({
          hostId: hostId
        });
        setUseDocker(executionInfo.useDocker || false);

        // 如果使用Docker模式，获取容器列表
        if (executionInfo.useDocker) {
          setTimeout(() => {
            fetchDockerContainers(hostId);
          }, 500);
        }

        if (executionInfo.containerName) {
          form.setFieldsValue({
            containerName: executionInfo.containerName
          });
        }
      }

      // 如果有初始日志路径，提取文件夹路径
      const logPath = initialLogPath || executionInfo.logFile;
      if (logPath) {
        const pathParts = logPath.split('/');
        const folderPathFromLog = pathParts.slice(0, -1).join('/');
        setFolderPath(folderPathFromLog);
        form.setFieldsValue({
          folderPath: folderPathFromLog
        });

        // 自动获取文件列表
        if (executionInfo.hostId) {
          setTimeout(() => {
            fetchFileList(folderPathFromLog);
          }, 1000);
        }
      }
    }
  }, [visible, initialLogPath, executionInfo]);

  const fetchHosts = async () => {
    try {
      const response = await http.get('/api/host/');
      const hostList = response || [];
      setHosts(hostList);

      // 如果没有从URL参数中获取hostId，则默认选择第一台主机
      if (hostList.length > 0 && !executionInfo.hostId) {
        form.setFieldsValue({
          hostId: hostList[0].id
        });
      }
    } catch (error) {
      message.error('获取主机列表失败: ' + error.message);
    }
  };

  // 获取Docker容器列表
  const fetchDockerContainers = async (hostId) => {
    if (!hostId) return;

    try {
      setLoadingContainers(true);
      const response = await http.get('/api/exec/docker-containers/', {
        params: { host_id: hostId }
      });

      if (response.success) {
        setDockerContainers(response.containers || []);
        // 清空之前选择的容器名称
        form.setFieldsValue({ containerName: undefined });
      } else {
        message.error('获取Docker容器列表失败: ' + (response.error || '未知错误'));
        setDockerContainers([]);
      }
    } catch (error) {
      console.error('获取Docker容器列表失败:', error);
      message.error('获取Docker容器列表失败: ' + (error.response?.data?.error || error.message));
      setDockerContainers([]);
    } finally {
      setLoadingContainers(false);
    }
  };

  // 处理主机选择变化
  const handleHostChange = (hostId) => {
    form.setFieldsValue({ hostId });

    // 如果选择了Docker模式，自动获取容器列表
    if (useDocker) {
      fetchDockerContainers(hostId);
    }
  };

  // 处理Docker模式切换
  const handleDockerModeChange = (checked) => {
    setUseDocker(checked);

    // 清空容器相关字段
    form.setFieldsValue({ containerName: undefined });
    setDockerContainers([]);

    // 如果切换到Docker模式且已选择主机，则获取容器列表
    if (checked) {
      const hostId = form.getFieldValue('hostId');
      if (hostId) {
        fetchDockerContainers(hostId);
      }
    }
  };

  // 获取路径自动补全建议
  const fetchPathSuggestions = async (partialPath) => {
    const hostId = form.getFieldValue('hostId');
    const containerName = form.getFieldValue('containerName');

    if (!hostId || (useDocker && !containerName)) {
      return;
    }

    try {
      const response = await http.get('/api/exec/docker-path-autocomplete/', {
        params: {
          host_id: hostId,
          container_name: containerName,
          path: partialPath
        }
      });

      if (response.suggestions) {
        setPathSuggestions(response.suggestions);
        setShowSuggestions(true);
      }
    } catch (error) {
      // 静默处理错误，不显示错误消息
      console.warn('获取路径建议失败:', error.message);
      setPathSuggestions([]);
    }
  };

  const fetchFileList = async (path) => {
    if (!path.trim()) {
      setFileList([]);
      return;
    }

    const hostId = form.getFieldValue('hostId');
    const containerName = form.getFieldValue('containerName');

    if (!hostId) {
      message.warning('请先选择目标主机');
      return;
    }

    if (useDocker && !containerName) {
      message.warning('请先选择Docker容器');
      return;
    }

    try {
      setLoadingFiles(true);

      const response = await http.get('/api/exec/docker-filesystem/', {
        params: {
          host_id: hostId,
          container_name: containerName,
          path: path
        }
      });

      if (response.success) {
        // 只显示文件，不显示目录
        const files = response.items.filter(item => !item.is_directory);
        setFileList(files.map(item => ({
          name: item.name,
          size: item.size || '-',
          isDirectory: item.is_directory,
          permissions: item.permissions,
          fullPath: item.full_path,
          isSymlink: item.is_symlink,
          linkTarget: item.link_target
        })));

        // 成功获取文件列表后，保存到最近使用目录
        if (files.length > 0) {
          saveRecentPath(path.trim());
        }
      } else {
        // 静默处理错误，不显示错误消息
        setFileList([]);
      }
    } catch (error) {
      // 静默处理网络错误，不显示错误消息
      console.warn('获取文件列表失败:', error.message);
      setFileList([]);
    } finally {
      setLoadingFiles(false);
    }
  };

  const handlePreviewFile = async (file) => {
    const hostId = form.getFieldValue('hostId');
    if (!hostId) {
      message.warning('请先选择目标主机');
      return;
    }

    try {
      setPreviewLoading(true);
      setPreviewFile(file);

      // 获取文件前几行作为预览
      const command = useDocker
        ? `docker exec ${form.getFieldValue('containerName')} head -20 "${file.fullPath}"`
        : `head -20 "${file.fullPath}"`;

      const response = await http.post('/api/exec/ssh/', {
        host_id: hostId,
        command: command
      });

      if (response.success) {
        setPreviewContent(response.data);
      } else {
        setPreviewContent('预览失败: ' + (response.error || '未知错误'));
      }
    } catch (error) {
      setPreviewContent('预览失败: ' + (error.response?.data?.error || error.message));
    } finally {
      setPreviewLoading(false);
    }
  };

  const handleFileSelection = (file, checked) => {
    try {
      console.log('[DEBUG] 文件选择:', file, checked);
      if (checked) {
        setSelectedFiles(prev => {
          const newFiles = [...prev, file];
          console.log('[DEBUG] 添加文件后的选中列表:', newFiles);
          return newFiles;
        });
      } else {
        setSelectedFiles(prev => {
          const newFiles = prev.filter(f => f.fullPath !== file.fullPath);
          console.log('[DEBUG] 移除文件后的选中列表:', newFiles);
          return newFiles;
        });
      }
    } catch (error) {
      console.error('[DEBUG] 文件选择错误:', error);
      message.error('文件选择失败: ' + error.message);
    }
  };

  // 全选/取消全选
  const handleSelectAll = (checked) => {
    try {
      console.log('[DEBUG] 全选操作:', checked, '文件列表长度:', fileList.length);
      setSelectAll(checked);
      if (checked) {
        setSelectedFiles([...fileList]);
        console.log('[DEBUG] 全选后的文件列表:', fileList);
      } else {
        setSelectedFiles([]);
        console.log('[DEBUG] 取消全选');
      }
    } catch (error) {
      console.error('[DEBUG] 全选操作错误:', error);
      message.error('全选操作失败: ' + error.message);
    }
  };

  // 更新全选状态
  useEffect(() => {
    if (fileList.length > 0) {
      setSelectAll(selectedFiles.length === fileList.length);
    } else {
      setSelectAll(false);
    }
  }, [selectedFiles, fileList]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 使用选中的文件路径
      const finalPaths = selectedFiles.map(file => file.fullPath);

      if (finalPaths.length === 0) {
        message.error('请至少选择一个日志文件');
        return;
      }

      console.log('[DEBUG] 多文件提取请求数据:', {
        log_paths: finalPaths,
        host_id: values.hostId,
        use_docker: useDocker,
        container_name: useDocker ? values.containerName : null
      });

      const response = await http.post('/api/exec/remote-log-fetch-multiple/', {
        log_paths: finalPaths,
        host_id: values.hostId,
        use_docker: useDocker,
        container_name: useDocker ? values.containerName : null
      });

      console.log('[DEBUG] 多文件提取响应:', response);

      if (response.success) {
        message.success(`成功获取 ${response.results.length} 个日志文件`);

        // 将多个文件的结果合并为一条记录
        const mergedResult = {
          id: `merged_${Date.now()}`,
          name: `批量分析结果_${selectedFiles.length}个文件`,
          description: `包含 ${selectedFiles.length} 个日志文件的批量分析结果`,
          results: response.results, // 保存所有文件的详细结果
          file_count: response.results.length,
          file_names: selectedFiles.map(f => f.name),
          created_time: new Date().toISOString(),
          host_info: response.host_info,
          use_docker: response.use_docker,
          container_name: response.container_name,
          is_batch: true // 标记为批量结果
        };

        onSuccess([mergedResult], {
          hostInfo: response.host_info,
          useDocker: response.use_docker,
          containerName: response.container_name
        });
      } else {
        throw new Error(response.error || '获取日志文件失败');
      }

    } catch (error) {
      console.error('[DEBUG] 请求失败:', error);
      const errorMessage = error.response?.data?.error || error.message || '未知错误';
      message.error('获取日志文件失败: ' + errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setLogPaths([]);
    setUseDocker(false);
    setFolderPath('');
    setFileList([]);
    setSelectedFiles([]);
    setPreviewFile(null);
    setPreviewContent('');
    setDockerContainers([]);
    setLoadingContainers(false);
    onCancel();
  };

  return (
    <Modal
      title={
        <div className={styles.modalTitle}>
          <SettingOutlined style={{ marginRight: '8px' }} />
          配置日志获取参数
        </div>
      }
      visible={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      okText="获取并分析日志"
      cancelText="取消"
      width={1000}
      confirmLoading={loading}
      className={styles.logExtractionConfig}
    >
      <div className={styles.configContent}>
        <Alert
          message="多文件日志提取"
          description="配置远程主机信息和日志文件路径，系统将自动获取并分析多个日志文件的性能指标"
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Form
          form={form}
          layout="vertical"
          initialValues={{
            logPaths: logPaths.join('\n')
          }}
        >
          <Form.Item
            label={
              <span>
                <CloudServerOutlined style={{ marginRight: '4px' }} />
                目标主机
              </span>
            }
            name="hostId"
            rules={[{ required: true, message: '请选择目标主机' }]}
          >
            <Select
              placeholder="请选择目标主机"
              showSearch
              optionFilterProp="children"
              onChange={handleHostChange}
            >
              {hosts.map(host => (
                <Option key={host.id} value={host.id}>
                  <Space>
                    <Tag color="blue">{host.name}</Tag>
                    <span>{host.hostname}</span>
                    <Tag color="green" size="small">{host.hostname}</Tag>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Divider orientation="left">文件来源</Divider>

          <div className={styles.dockerConfig}>
            <div className={styles.dockerSwitch}>
              <Switch
                checked={useDocker}
                onChange={handleDockerModeChange}
                checkedChildren="Docker容器"
                unCheckedChildren="主机文件系统"
              />
              <span className={styles.dockerLabel}>
                {useDocker ? '从Docker容器获取日志' : '从主机文件系统获取日志'}
              </span>
            </div>

            {useDocker && (
              <Form.Item
                label="Docker容器名称"
                name="containerName"
                rules={[{ required: useDocker, message: '请选择Docker容器' }]}
                style={{ marginTop: '16px' }}
              >
                <Select
                  placeholder={loadingContainers ? "正在获取容器列表..." : "请选择Docker容器"}
                  loading={loadingContainers}
                  disabled={loadingContainers || dockerContainers.length === 0}
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                  notFoundContent={
                    dockerContainers.length === 0 && !loadingContainers
                      ? "未找到运行中的容器"
                      : null
                  }
                >
                  {dockerContainers.map(container => (
                    <Option key={container.name} value={container.name}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span style={{ fontWeight: 'bold' }}>{container.name}</span>
                        <div style={{ fontSize: '12px', color: '#666', marginLeft: '8px' }}>
                          <div>{container.image}</div>
                          <div>{container.status}</div>
                        </div>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            )}
          </div>

          <Divider orientation="left">日志文件选择</Divider>

          {/* 最近使用目录 */}
          {recentPaths.length > 0 && (
            <div style={{ marginBottom: '16px' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '8px',
                fontSize: '14px',
                color: '#666'
              }}>
                <HistoryOutlined style={{ marginRight: '4px' }} />
                最近使用目录
                <Button
                  type="link"
                  size="small"
                  onClick={clearRecentPaths}
                  style={{ marginLeft: '8px', padding: '0', height: 'auto' }}
                >
                  清除
                </Button>
              </div>
              <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                {recentPaths.map((path, index) => (
                  <Button
                    key={index}
                    size="small"
                    type="default"
                    icon={<FolderOutlined />}
                    onClick={() => {
                      setFolderPath(path);
                      form.setFieldsValue({ folderPath: path });
                      fetchFileList(path);
                    }}
                    style={{
                      maxWidth: '300px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                    title={path}
                  >
                    {path}
                  </Button>
                ))}
              </div>
            </div>
          )}

          <Form.Item
            label={
              <span>
                <FolderOutlined style={{ marginRight: '4px' }} />
                Docker容器内日志文件路径（每行一个）
              </span>
            }
            name="folderPath"
            rules={[{ required: true, message: '请输入文件夹路径' }]}
          >
            <AutoComplete
              placeholder="请输入文件夹路径，例如：/home，支持智能补全"
              value={folderPath}
              options={pathSuggestions.map(suggestion => ({
                value: suggestion.path,
                label: (
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <span>
                      <FolderOutlined style={{ marginRight: '4px', color: suggestion.is_directory ? '#1890ff' : '#666' }} />
                      {suggestion.name}
                    </span>
                    <Tag size="small" color={suggestion.is_directory ? 'blue' : 'default'}>
                      {suggestion.type}
                    </Tag>
                  </div>
                )
              }))}
              onSearch={(value) => {
                if (value && value.length > 0) {
                  fetchPathSuggestions(value);
                } else {
                  setShowSuggestions(false);
                  setPathSuggestions([]);
                }
              }}
              onSelect={(value) => {
                setFolderPath(value);
                setShowSuggestions(false);
                // 如果选择的是目录，不立即获取文件列表，让用户继续输入
                if (!value.endsWith('/')) {
                  fetchFileList(value);
                }
              }}
              onChange={(value) => {
                setFolderPath(value);
                if (value && value.trim()) {
                  fetchFileList(value.trim());
                } else {
                  setFileList([]);
                  setSelectedFiles([]);
                }
              }}
              onBlur={() => {
                setTimeout(() => setShowSuggestions(false), 200);
              }}
              open={showSuggestions && pathSuggestions.length > 0}
              dropdownStyle={{ maxHeight: '300px' }}
            >
              <Input
                suffix={
                  <Button
                    type="text"
                    size="small"
                    icon={<ReloadOutlined />}
                    onClick={() => fetchFileList(folderPath)}
                    loading={loadingFiles}
                  />
                }
              />
            </AutoComplete>
          </Form.Item>

          {/* 文件列表 */}
          <div className={styles.fileListContainer}>
            <div className={styles.fileListHeader}>
              <div className={styles.selectAllContainer}>
                {fileList.length > 0 && (
                  <Checkbox
                    checked={selectAll}
                    indeterminate={selectedFiles.length > 0 && selectedFiles.length < fileList.length}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  >
                    全选
                  </Checkbox>
                )}
                <span>文件列表 ({fileList.length})</span>
              </div>
              {selectedFiles.length > 0 && (
                <Tag color="purple">已选择 {selectedFiles.length} 个文件</Tag>
              )}
            </div>

            <div className={styles.fileListContent}>
              {loadingFiles ? (
                <div className={styles.loadingContainer}>
                  <Spin size="small" />
                  <span style={{ marginLeft: '8px' }}>加载文件列表...</span>
                </div>
              ) : fileList.length > 0 ? (
                <List
                  size="small"
                  dataSource={fileList}
                  renderItem={(file) => (
                    <List.Item
                      className={styles.fileItem}
                      actions={[
                        <Tooltip title="预览文件内容">
                          <Button
                            type="text"
                            size="small"
                            icon={<EyeOutlined />}
                            onClick={() => handlePreviewFile(file)}
                          />
                        </Tooltip>
                      ]}
                    >
                      <div className={styles.fileItemContent}>
                        <Checkbox
                          checked={selectedFiles.some(f => f.fullPath === file.fullPath)}
                          onChange={(e) => handleFileSelection(file, e.target.checked)}
                        />
                        <div className={styles.fileInfo}>
                          <div className={styles.fileName}>{file.name}</div>
                          <div className={styles.fileDetails}>
                            <Tag size="small" color="blue">{file.size}</Tag>
                            <span className={styles.filePath}>{file.fullPath}</span>
                          </div>
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
              ) : folderPath ? (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="该文件夹下没有找到文件"
                />
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="请输入文件夹路径以查看文件列表"
                />
              )}
            </div>
          </div>

          {/* 文件预览 */}
          {previewFile && (
            <Card
              title={`预览: ${previewFile.name}`}
              size="small"
              className={styles.previewCard}
              extra={
                <Button
                  type="text"
                  size="small"
                  onClick={() => {
                    setPreviewFile(null);
                    setPreviewContent('');
                  }}
                >
                  关闭
                </Button>
              }
            >
              {previewLoading ? (
                <div className={styles.loadingContainer}>
                  <Spin size="small" />
                  <span style={{ marginLeft: '8px' }}>加载预览...</span>
                </div>
              ) : (
                <pre className={styles.previewContent}>{previewContent}</pre>
              )}
            </Card>
          )}
        </Form>

        {/* VTable 高性能日志分析表格 */}
        {useDocker && extractionMode === 'multiple' && (
          <LogAnalysisTable
            selectedFiles={selectedFiles}
            hostId={form.getFieldValue('hostId')}
            containerName={form.getFieldValue('containerName')}
          />
        )}
      </div>
    </Modal>
  );
}

export default LogExtractionConfig;
