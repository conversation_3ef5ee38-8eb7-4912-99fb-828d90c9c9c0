#!/usr/bin/env python
"""
初始化Word模板数据的脚本
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.model_storage.models import WordTemplate, TemplateVariable
import json

def create_h3c_template():
    """创建H3C测试指导模板"""
    try:
        # 检查模板是否已存在
        existing_template = WordTemplate.objects.filter(name='H3C异构服务器测试指导模板').first()
        if existing_template:
            print("H3C模板已存在，更新变量配置")
            # 删除旧的变量配置
            TemplateVariable.objects.filter(template=existing_template).delete()
            template = existing_template
        else:
            # 创建新模板
            template = WordTemplate.objects.create(
                name='H3C异构服务器测试指导模板',
                description='基于H3C异构服务器厂商加速卡模型推理/训练测试指导文档的模板',
                template_file=r'c:\Users\<USER>\Documents\GitHub\spug\H3C_Template_Base.docx',
                template_type='test_guide',
                status='active',
                created_by='system'
            )
            print(f"模板创建成功，ID: {template.id}")
        
        # 创建模板
        template = WordTemplate.objects.create(
            name='H3C异构服务器测试指导模板',
            description='基于H3C异构服务器厂商加速卡模型推理/训练测试指导文档的模板',
            template_file=r'c:\Users\<USER>\Documents\GitHub\spug\H3C_Template_Base.docx',
            template_type='test_guide',
            status='active',
            created_by='system'
        )
        
        print(f"模板创建成功，ID: {template.id}")
        
        # 创建模板变量
        variables_data = [
            {
                'variable_name': '厂商',
                'display_name': '厂商名称',
                'variable_type': 'vendor',
                'group_name': '基本信息',
                'sort_order': 1,
                'help_text': '请输入厂商名称，如：华为、百度等',
                'placeholder': '请输入厂商名称'
            },
            {
                'variable_name': 'xxx',
                'display_name': '加速卡型号',
                'variable_type': 'gpu_model',
                'group_name': '基本信息',
                'sort_order': 2,
                'help_text': '请输入加速卡型号，如：P800、RG800等',
                'placeholder': '请输入加速卡型号'
            },
            {
                'variable_name': '常规模型',
                'display_name': '模型类型',
                'variable_type': 'text',
                'group_name': '基本信息',
                'sort_order': 3,
                'help_text': '请输入模型类型，如：ResNet50、BERT等',
                'placeholder': '请输入模型类型'
            },
            {
                'variable_name': '内容',
                'display_name': '内容',
                'variable_type': 'textarea',
                'group_name': '基本信息',
                'sort_order': 4,
                'help_text': '请输入相关内容描述',
                'placeholder': '请输入内容'
            },
            {
                'variable_name': '推理|训练',
                'display_name': '测试类型',
                'variable_type': 'select',
                'options': json.dumps(['推理', '训练']),
                'group_name': '测试配置',
                'sort_order': 4,
                'help_text': '请选择测试类型：推理或训练',
                'placeholder': '请选择测试类型'
            },
            {
                'variable_name': 'GPU',
                'display_name': 'GPU型号',
                'variable_type': 'gpu_model',
                'group_name': 'GPU配置',
                'sort_order': 5,
                'help_text': '请输入GPU型号，如：P800、RG800等',
                'placeholder': '请输入GPU型号'
            },
        ]
        
        for var_data in variables_data:
            TemplateVariable.objects.create(
                template=template,
                variable_name=var_data['variable_name'],
                display_name=var_data['display_name'],
                variable_type=var_data['variable_type'],
                default_value='',
                options=var_data.get('options', '[]'),
                is_required=True,
                sort_order=var_data['sort_order'],
                group_name=var_data['group_name'],
                help_text=var_data['help_text'],
                placeholder=var_data['placeholder']
            )
        
        print(f"模板变量创建完成，共创建 {len(variables_data)} 个变量")
        print("H3C模板初始化成功！")
        
    except Exception as e:
        print(f"创建模板失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    create_h3c_template()
