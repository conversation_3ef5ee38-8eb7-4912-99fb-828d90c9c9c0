#!/usr/bin/env python3
"""
逐步调试API
"""
import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.models import Detection
from django_redis import get_redis_connection
from datetime import datetime

def debug_api_step_by_step():
    """逐步调试API"""
    print("=== 逐步调试API ===\n")
    
    # 完全按照API函数的逻辑来执行
    response = []
    rds = get_redis_connection()
    
    print("📊 开始处理监控任务...")
    
    for item in Detection.objects.all():
        print(f"\n🔍 处理监控任务: {item.name} (ID: {item.id})")
        print(f"   是否激活: {item.is_active}")
        print(f"   目标: {item.targets}")
        print(f"   最后运行: {item.latest_run_time}")
        
        data = {}
        
        # 解析目标
        try:
            targets = json.loads(item.targets)
            print(f"   解析后的目标: {targets}")
        except Exception as e:
            print(f"   ❌ 目标解析失败: {e}")
            continue
        
        # 处理每个目标
        for key in targets:
            key = str(key)
            print(f"\n   处理目标: {key}")
            
            data[key] = {
                'id': f'{item.id}_{key}',
                'group': item.group,
                'name': item.name,
                'type': item.get_type_display(),
                'target': key,
                'desc': item.desc,
                'status': '0',
                'latest_run_time': item.latest_run_time,
                'process_info': None,
            }
            print(f"     初始化数据: {data[key]['id']}")
            
            # 获取进程详细信息（仅对智能进程检测）
            if item.type == '6':
                process_info_key = f'spug:det:info:{item.id}_{key}'
                print(f"     查找进程信息: {process_info_key}")
                
                process_info_data = rds.get(process_info_key)
                if process_info_data:
                    try:
                        process_info = json.loads(process_info_data.decode())
                        data[key]['process_info'] = process_info
                        print(f"     ✅ 找到进程信息: {len(process_info)} 个进程")
                    except Exception as e:
                        print(f"     ❌ 进程信息解析失败: {e}")
                else:
                    print(f"     ⚠️ 没有找到进程信息")
            
            # 设置状态
            if item.is_active:
                if item.latest_run_time:
                    data[key]['status'] = '1'
                    print(f"     状态设置为: 正常 (1)")
                else:
                    data[key]['status'] = '10'
                    print(f"     状态设置为: 未运行 (10)")
            else:
                print(f"     监控任务未激活，状态保持为: {data[key]['status']}")
        
        print(f"\n   当前data内容: {len(data)} 个目标")
        for k, v in data.items():
            print(f"     {k}: {v['name']} (状态: {v['status']})")
        
        # 检查Redis中的监控状态
        if item.is_active:
            print(f"\n   监控任务已激活，检查Redis状态...")
            monitor_key = f'spug:det:{item.id}'
            print(f"   检查监控状态key: {monitor_key}")
            
            monitor_data = rds.hgetall(monitor_key)
            print(f"   Redis状态数据: {len(monitor_data)} 个字段")
            
            if monitor_data:
                for k, v in monitor_data.items():
                    key_str = k.decode()
                    val_str = v.decode()
                    print(f"     {key_str}: {val_str}")
                    
                    if '_' in key_str:
                        prefix, target_key = key_str.split('_', 1)
                        print(f"       解析: prefix={prefix}, target_key={target_key}")
                        
                        if target_key in data:
                            val = int(val_str)
                            if prefix == 'c':
                                if data[target_key]['status'] == '1':
                                    data[target_key]['status'] = '2'
                                data[target_key]['count'] = val
                                print(f"       更新目标 {target_key} 状态为警告，错误计数: {val}")
                            elif prefix == 't':
                                date = datetime.fromtimestamp(val).strftime('%Y-%m-%d %H:%M:%S')
                                data[target_key].update(status='3', notified_at=date)
                                print(f"       更新目标 {target_key} 状态为故障，通知时间: {date}")
                        else:
                            print(f"       ⚠️ 目标key {target_key} 不在data中")
            else:
                print(f"   没有Redis状态数据")
            
            # 添加到响应
            data_values = list(data.values())
            response.extend(data_values)
            print(f"   ✅ 添加了 {len(data_values)} 个监控项到响应")
        else:
            print(f"   ❌ 监控任务未激活，不添加到响应")
    
    print(f"\n📋 最终响应: {len(response)} 个监控项")
    for i, item in enumerate(response):
        print(f"  {i+1}. {item['name']}:")
        print(f"     ID: {item['id']}")
        print(f"     状态: {item['status']}")
        print(f"     进程信息: {'有' if item['process_info'] else '无'}")

if __name__ == '__main__':
    debug_api_step_by_step()
