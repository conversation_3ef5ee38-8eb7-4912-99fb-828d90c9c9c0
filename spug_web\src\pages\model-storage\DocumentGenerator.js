import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import {
  Card,
  Button,
  Form,
  Input,
  Select,
  Space,
  Typography,
  Alert,
  Progress,
  message,
  Row,
  Col,
  InputNumber,
  DatePicker,
  Switch
} from 'antd';
import {
  FileWordOutlined,
  SendOutlined,
  ReloadOutlined,
  SaveOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import http from '../../libs/http';
import GpuSelector from '../../components/GpuSelector';
import SimpleWordEditor from './components/SimpleWordEditor';
import TextFormatter from './components/TextFormatter';

const { Option } = Select;
const { Title, Text } = Typography;
const { TextArea } = Input;

export default observer(function DocumentGenerator() {
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [templateVariables, setTemplateVariables] = useState([]);
  const [generating, setGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [draftId, setDraftId] = useState(null);
  const [richTextValues, setRichTextValues] = useState({}); // 存储富文本编辑器的值

  const [form] = Form.useForm();

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      const res = await http.get('/api/model-storage/word-templates/');
      console.log('🔍 获取模板列表响应:', res);
      
      // 由于http拦截器会直接返回data字段，所以res就是数据数组
      const templates = Array.isArray(res) ? res : (res.data || []);
      const activeTemplates = templates.filter(t => t.status === 'active');
      
      console.log('🔍 所有模板:', templates);
      console.log('🔍 Active模板:', activeTemplates);
      
      setTemplates(activeTemplates);
    } catch (error) {
      console.error('❌ 获取模板列表失败:', error);
      message.error('获取模板列表失败：' + error.message);
    }
  };

  const fetchTemplateVariables = async (templateId) => {
    try {
      const res = await http.get(`/api/model-storage/word-templates/?id=${templateId}`);

      // 由于http拦截器会直接返回data字段，所以res就是模板对象
      const templateData = res;
      const variables = templateData.variables || [];

      setTemplateVariables(variables);

      // 设置变量默认值
      const defaultValues = {};
      variables.forEach(variable => {
        if (variable.default_value) {
          defaultValues[`var_${variable.variable_name}`] = variable.default_value;
        }
      });

      form.setFieldsValue(defaultValues);
    } catch (error) {
      console.error('❌ 获取模板变量失败:', error);
      message.error('获取模板变量失败：' + error.message);
    }
  };

  const handleTemplateSelect = async (templateId) => {
    const template = templates.find(t => t.id === templateId);
    setSelectedTemplate(template);

    if (template) {
      await fetchTemplateVariables(templateId);
      // 设置表单的模板相关字段
      form.setFieldsValue({
        template_id: templateId,
        title: template.name // 默认使用模板名称作为文档标题
      });

      // 清空之前的草稿ID，因为选择了新模板
      setDraftId(null);
      setLastSaved(null);
    }
  };

  // 保存草稿
  const handleSaveDraft = async () => {
    try {
      setSaving(true);

      // 获取表单数据
      const values = form.getFieldsValue();

      // 提取变量值
      const variables = {};
      Object.keys(values).forEach(key => {
        if (key.startsWith('var_')) {
          const varName = key.substring(4);
          variables[varName] = values[key];
        }
      });

      const draftData = {
        title: values.title || '未命名草稿',
        template_id: values.template_id,
        variables_values: variables,
        rich_content: values.content || '',
        status: 'draft'
      };

      let response;
      if (draftId) {
        // 更新现有草稿 - 将ID放在数据中
        response = await http.put(`/api/model-storage/document-instances/${draftId}/`, {
          ...draftData,
          id: draftId
        });
      } else {
        // 创建新草稿
        response = await http.post('/api/model-storage/document-instances/', draftData);
        setDraftId(response.id);
      }

      setLastSaved(new Date());
      message.success('草稿保存成功');
    } catch (error) {
      console.error('保存草稿失败:', error);
      message.error('保存草稿失败：' + error.message);
    } finally {
      setSaving(false);
    }
  };

  // 自动保存草稿（每30秒）
  useEffect(() => {
    if (!selectedTemplate) return;

    const autoSaveInterval = setInterval(() => {
      const values = form.getFieldsValue();
      // 检查是否有内容需要保存
      const hasContent = values.title ||
                        Object.keys(values).some(key =>
                          key.startsWith('var_') && values[key]
                        );

      if (hasContent) {
        handleSaveDraft();
      }
    }, 30000); // 30秒自动保存

    return () => clearInterval(autoSaveInterval);
  }, [selectedTemplate, draftId]);

  const handleGenerate = async () => {
    try {
      setGenerating(true);
      setGenerationProgress(0);
      
      // 验证表单
      const values = await form.validateFields();

      // 提取变量值
      const variables = {};
      Object.keys(values).forEach(key => {
        if (key.startsWith('var_')) {
          const varName = key.substring(4); // 移除 'var_' 前缀
          variables[varName] = values[key];
        }
      });
      
      // 模拟生成进度
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);
      
      // 调用生成API
      const generateData = {
        template_id: values.template_id,
        title: values.title,
        variables: variables,
        content: values.content || ''
      };

      const response = await http.post('/api/model-storage/word-templates/generate/', generateData);

      clearInterval(progressInterval);
      setGenerationProgress(100);
      
      // 下载文件
      if (response.download_url) {
        const link = document.createElement('a');
        link.href = response.download_url;
        link.download = `${values.title}.docx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        message.success('文档生成成功！');
      } else {
        message.error('文档生成失败：未获取到下载链接');
      }
      
    } catch (error) {
      console.error('❌ 文档生成失败:', error);
      message.error('文档生成失败：' + error.message);
    } finally {
      setGenerating(false);
      setTimeout(() => setGenerationProgress(0), 2000);
    }
  };

  const renderVariableInput = (variable) => {
    const fieldName = `var_${variable.variable_name}`;
    const displayName = variable.display_name || variable.variable_name || '未命名字段';

    // 检查是否是GPU相关变量
    const isGpuVariable = variable.variable_name.toLowerCase().includes('gpu') ||
                         displayName.toLowerCase().includes('gpu') ||
                         displayName.toLowerCase().includes('加速卡');

    // 检查是否是富文本变量 - 只有明确标记为richtext或包含"正文"的才是富文本
    const isRichTextVariable = variable.variable_type === 'richtext' ||
                              variable.variable_name.toLowerCase().includes('正文') ||
                              displayName.toLowerCase().includes('正文');

    // 富文本变量单独处理，不在这里渲染
    if (isRichTextVariable) {
      return null;
    }

    switch (variable.variable_type) {
      case 'number':
        return (
          <Form.Item
            key={fieldName}
            name={fieldName}
            label={displayName}
            rules={[{ required: variable.is_required, message: `请输入${displayName}` }]}
            tooltip={variable.description}
          >
            <InputNumber
              placeholder={`请输入${displayName}`}
              style={{ width: '100%' }}
            />
          </Form.Item>
        );

      case 'date':
        return (
          <Form.Item
            key={fieldName}
            name={fieldName}
            label={displayName}
            rules={[{ required: variable.is_required, message: `请选择${displayName}` }]}
            tooltip={variable.description}
          >
            <DatePicker
              placeholder={`请选择${displayName}`}
              style={{ width: '100%' }}
            />
          </Form.Item>
        );

      case 'boolean':
        return (
          <Form.Item
            key={fieldName}
            name={fieldName}
            label={displayName}
            valuePropName="checked"
            tooltip={variable.description}
          >
            <Switch />
          </Form.Item>
        );

      case 'textarea':
        return (
          <Form.Item
            key={fieldName}
            name={fieldName}
            label={displayName}
            rules={[{ required: variable.is_required, message: `请输入${displayName}` }]}
            tooltip={variable.description}
          >
            <TextArea
              rows={4}
              placeholder={`请输入${displayName}`}
            />
          </Form.Item>
        );

      default:
        // GPU变量使用GPU选择器
        if (isGpuVariable) {
          return (
            <Form.Item
              key={fieldName}
              name={fieldName}
              label={displayName}
              rules={[{ required: variable.is_required, message: `请选择${displayName}` }]}
              tooltip={variable.description}
            >
              <GpuSelector
                placeholder={`请选择${displayName}`}
                style={{ width: '100%' }}
              />
            </Form.Item>
          );
        }

        // 普通文本输入
        return (
          <Form.Item
            key={fieldName}
            name={fieldName}
            label={displayName}
            rules={[{ required: variable.is_required, message: `请输入${displayName}` }]}
            tooltip={variable.description}
          >
            <Input
              placeholder={`请输入${displayName}`}
            />
          </Form.Item>
        );
    }
  };

  // 渲染富文本变量 - 只有明确的富文本变量
  const renderRichTextVariables = () => {
    const richTextVariables = templateVariables.filter(variable => {
      const displayName = variable.display_name || variable.variable_name;
      // 只识别明确标记为richtext或包含"正文"的变量
      return variable.variable_type === 'richtext' ||
             variable.variable_name.toLowerCase().includes('正文') ||
             displayName.toLowerCase().includes('正文');
    });

    if (richTextVariables.length === 0) {
      return null;
    }

    return richTextVariables.map(variable => {
      const fieldName = `var_${variable.variable_name}`;
      const displayName = variable.display_name || variable.variable_name;

      // 检查是否是"正文"相关的变量（需要完整的富文本编辑器）
      const isRichTextContent = variable.variable_name.toLowerCase().includes('正文') ||
                               displayName.toLowerCase().includes('正文');

      return (
        <Form.Item
          key={fieldName}
          name={fieldName}
          label={displayName}
          rules={[{ required: variable.is_required, message: `请输入${displayName}` }]}
          tooltip={variable.description}
        >
          {isRichTextContent ? (
            // 正文相关变量：显示格式规范工具 + 富文本编辑器
            <div>
              <TextFormatter
                placeholder={`请输入${displayName}`}
                onChange={(value) => {
                  // 避免循环更新：只有当值真正改变时才更新
                  if (value !== richTextValues[fieldName]) {
                    form.setFieldsValue({ [fieldName]: value });
                    setRichTextValues(prev => ({
                      ...prev,
                      [fieldName]: value
                    }));
                  }
                }}
              />
              <div style={{ marginTop: 8 }}>
                <SimpleWordEditor
                  placeholder={`请输入${displayName}`}
                  height={250}
                  value={richTextValues[fieldName] || ''}
                  onChange={(value) => {
                    // 避免循环更新：只有当值真正改变时才更新
                    if (value !== richTextValues[fieldName]) {
                      form.setFieldsValue({ [fieldName]: value });
                      setRichTextValues(prev => ({
                        ...prev,
                        [fieldName]: value
                      }));
                    }
                  }}
                />
              </div>
            </div>
          ) : (
            // 其他富文本变量：只显示简单编辑器
            <SimpleWordEditor
              placeholder={`请输入${displayName}`}
              height={200}
              value={richTextValues[fieldName] || ''}
              onChange={(value) => {
                // 编辑器变化时同步到表单
                form.setFieldsValue({ [fieldName]: value });
                setRichTextValues(prev => ({
                  ...prev,
                  [fieldName]: value
                }));
              }}
            />
          )}
        </Form.Item>
      );
    });
  };

  // 按组分类变量
  const groupVariables = () => {
    const groups = {};
    const regularVariables = templateVariables.filter(variable => {
      const displayName = variable.display_name || variable.variable_name;
      // 与renderVariableInput保持一致的富文本识别逻辑 - 只有明确标记为richtext或包含"正文"的才是富文本
      const isRichText = variable.variable_type === 'richtext' ||
                        variable.variable_name.toLowerCase().includes('正文') ||
                        displayName.toLowerCase().includes('正文');
      return !isRichText;
    });

    regularVariables.forEach(variable => {
      const groupName = variable.group_name || '其他';
      if (!groups[groupName]) {
        groups[groupName] = [];
      }
      groups[groupName].push(variable);
    });

    return groups;
  };

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <Card 
        style={{ maxWidth: '1200px', margin: '0 auto' }}
        title={
          <Space>
            <FileWordOutlined style={{ color: '#1890ff' }} />
            <Title level={3} style={{ margin: 0 }}>Word文档生成器</Title>
          </Space>
        }
        extra={
          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchTemplates}
            loading={loading}
          >
            刷新模板
          </Button>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleGenerate}
        >
          {/* 基本信息区域 */}
          <Card 
            title="基本信息" 
            size="small" 
            style={{ marginBottom: '16px' }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="template_id"
                  label="选择模板"
                  rules={[{ required: true, message: '请选择模板' }]}
                >
                  <Select
                    placeholder="请选择Word模板"
                    onChange={handleTemplateSelect}
                    size="large"
                    loading={loading}
                  >
                    {templates.map(template => (
                      <Option key={template.id} value={template.id}>
                        <Space>
                          <FileWordOutlined />
                          <div>
                            <div>{template.name}</div>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {template.description}
                            </Text>
                          </div>
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="title"
                  label="文档标题"
                  rules={[{ required: true, message: '请输入文档标题' }]}
                >
                  <Input 
                    placeholder="请输入生成文档的标题" 
                    size="large"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          {/* 模板变量区域 */}
          {selectedTemplate && (
            <>
              {templateVariables.length > 0 ? (
                <>
                  {/* 按组显示普通变量 */}
                  {Object.entries(groupVariables()).map(([groupName, variables]) => (
                    <Card
                      key={groupName}
                      title={`${groupName} (${variables.length}个变量)`}
                      size="small"
                      style={{ marginBottom: '16px' }}
                    >
                      <Row gutter={16}>
                        {variables.map((variable) => {
                          const renderedInput = renderVariableInput(variable);
                          if (!renderedInput) return null;

                          return (
                            <Col
                              key={variable.variable_name}
                              span={variables.length === 1 ? 24 : (variables.length === 2 ? 12 : 8)}
                            >
                              {renderedInput}
                            </Col>
                          );
                        })}
                      </Row>
                    </Card>
                  ))}

                  {/* 富文本变量区域 */}
                  {renderRichTextVariables() && (
                    <Card
                      title="富文本"
                      size="small"
                      style={{ marginBottom: '16px' }}
                    >
                      {renderRichTextVariables()}
                    </Card>
                  )}
                </>
              ) : (
                <Card
                  title="模板变量配置"
                  size="small"
                  style={{ marginBottom: '16px' }}
                >
                  <Alert
                    message="调试信息"
                    description={
                      <div>
                        <p>模板已选择但没有变量数据</p>
                        <p>请打开浏览器控制台查看详细日志</p>
                        <p>模板ID: {selectedTemplate.id}</p>
                        <p>模板名称: {selectedTemplate.name}</p>
                      </div>
                    }
                    type="warning"
                    showIcon
                  />
                </Card>
              )}
            </>
          )}

          {/* 附加内容区域 - 如果没有富文本变量，提供一个通用的富文本输入 */}
          {selectedTemplate && renderRichTextVariables() === null && (
            <Card
              title="附加内容 (可选)"
              size="small"
              style={{ marginBottom: '16px' }}
            >
              <Form.Item
                name="content"
                label="附加富文本内容"
                tooltip="可以添加额外的富文本内容到文档中，将显示在框框里"
              >
                <div>
                  <TextFormatter
                    placeholder="请输入要添加到文档中的额外内容..."
                    onChange={(value) => {
                      // 避免循环更新：只有当值真正改变时才更新
                      if (value !== richTextValues.content) {
                        form.setFieldsValue({ content: value });
                        setRichTextValues(prev => ({
                          ...prev,
                          content: value
                        }));
                      }
                    }}
                  />
                  <div style={{ marginTop: 8 }}>
                    <SimpleWordEditor
                      placeholder="请输入要添加到文档中的额外内容..."
                      height={250}
                      value={richTextValues.content || ''}
                      onChange={(value) => {
                        // 避免循环更新：只有当值真正改变时才更新
                        if (value !== richTextValues.content) {
                          form.setFieldsValue({ content: value });
                          setRichTextValues(prev => ({
                            ...prev,
                            content: value
                          }));
                        }
                      }}
                    />
                  </div>
                </div>
              </Form.Item>
            </Card>
          )}

          {/* 生成按钮和进度 */}
          <Card size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              {generating && (
                <Progress
                  percent={generationProgress}
                  status={generationProgress === 100 ? 'success' : 'active'}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              )}

              <div style={{ textAlign: 'center' }}>
                <Space size="large">
                  <Button
                    icon={<SaveOutlined />}
                    loading={saving}
                    onClick={handleSaveDraft}
                    disabled={!selectedTemplate || generating}
                  >
                    {saving ? '保存中...' : '保存草稿'}
                  </Button>

                  <Button
                    type="primary"
                    size="large"
                    icon={<SendOutlined />}
                    loading={generating}
                    htmlType="submit"
                    disabled={!selectedTemplate}
                    style={{ minWidth: '200px' }}
                  >
                    {generating ? '正在生成文档...' : '生成Word文档'}
                  </Button>
                </Space>
              </div>

              {/* 草稿保存状态 */}
              {lastSaved && (
                <div style={{ textAlign: 'center', marginTop: '8px' }}>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    <ClockCircleOutlined style={{ marginRight: '4px' }} />
                    最后保存: {lastSaved.toLocaleTimeString()}
                    {draftId && ` (草稿ID: ${draftId})`}
                  </Text>
                </div>
              )}
            </Space>
          </Card>
        </Form>

        {/* 模板信息展示 */}
        {selectedTemplate && (
          <Alert
            style={{ marginTop: '16px' }}
            message="当前选择的模板"
            description={
              <div>
                <p><strong>模板名称：</strong>{selectedTemplate.name}</p>
                <p><strong>模板描述：</strong>{selectedTemplate.description}</p>
                <p><strong>模板类型：</strong>{selectedTemplate.template_type}</p>
                <p><strong>变量数量：</strong>{templateVariables.length}个</p>
              </div>
            }
            type="info"
            showIcon
          />
        )}
      </Card>
    </div>
  );
});
