#!/usr/bin/env python3
"""
测试进程检测
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.executors import smart_process_check
from apps.host.models import Host

def test_process_check():
    """测试进程检测"""
    print("=== 测试进程检测 ===\n")
    
    # 获取主机
    host = Host.objects.get(pk=1)
    print(f"🖥️ 目标主机: {host.name} ({host.hostname})")
    
    # 测试不同的搜索模式
    test_cases = [
        ('run_28k_4k_qwen2.sh', 'Qwen3'),
        ('run_28k_4k_qwen2', 'Qwen3'),
        ('bash', 'Qwen3'),
        ('python', 'Qwen3'),
    ]
    
    for process_name, container_name in test_cases:
        print(f"\n🔍 测试: 进程='{process_name}', 容器='{container_name}'")
        
        try:
            is_found, message = smart_process_check(host, process_name, container_name)
            
            print(f"结果: {'✅ 找到' if is_found else '❌ 未找到'}")
            print(f"消息长度: {len(message)} 字符")
            print(f"消息内容:")
            print("=" * 50)
            print(message)
            print("=" * 50)
            
            # 检查消息格式是否符合存储要求
            if '找到' in message and '个匹配的进程' in message:
                print("✅ 消息格式符合存储要求")
                
                # 模拟解析过程
                lines = message.split('\n')
                process_details = []
                current_process = {}
                
                for line in lines:
                    line = line.strip()
                    if line.startswith('进程 '):
                        if current_process:
                            process_details.append(current_process)
                        current_process = {}
                    elif line.startswith('PID: '):
                        current_process['pid'] = line.replace('PID: ', '')
                    elif line.startswith('命令: '):
                        current_process['cmd'] = line.replace('命令: ', '')
                    elif line.startswith('运行时间: '):
                        current_process['etime'] = line.replace('运行时间: ', '')
                    elif line.startswith('位置: '):
                        current_process['location'] = line.replace('位置: ', '')
                
                if current_process:
                    process_details.append(current_process)
                
                print(f"解析出的进程详情: {len(process_details)} 个进程")
                for i, proc in enumerate(process_details):
                    print(f"  进程 {i+1}: {proc}")
            else:
                print("❌ 消息格式不符合存储要求")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_process_check()
