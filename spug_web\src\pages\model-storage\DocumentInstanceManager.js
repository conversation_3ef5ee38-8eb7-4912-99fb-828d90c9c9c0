import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  message, 
  Popconfirm,
  Tooltip,
  Row,
  Col,
  Statistic,
  Typography,
  Input,
  Select,
  DatePicker
} from 'antd';
import { 
  FileWordOutlined,
  DownloadOutlined,
  DeleteOutlined,
  EyeOutlined,
  EditOutlined,
  CopyOutlined,
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import http from '../../libs/http';
import styles from './DocumentInstanceManager.module.less';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;

export default observer(function DocumentInstanceManager() {
  const [loading, setLoading] = useState(false);
  const [instances, setInstances] = useState([]);
  const [templates, setTemplates] = useState([]);
  const [statistics, setStatistics] = useState({
    total: 0,
    draft: 0,
    generated: 0,
    exported: 0
  });
  const [filters, setFilters] = useState({
    search: '',
    template_id: null,
    status: null,
    date_range: null
  });

  useEffect(() => {
    fetchInstances();
    fetchTemplates();
  }, []);

  const fetchInstances = async () => {
    setLoading(true);
    try {
      const res = await http.get('/api/model-storage/document-instances/');

      if (res.error) {
        message.error(res.error);
      } else {
        // 检查响应格式：可能是直接数组，也可能是包含data字段的对象
        let instancesData = [];
        if (Array.isArray(res)) {
          // 直接是数组
          instancesData = res;
        } else if (res.data && Array.isArray(res.data)) {
          // 包含data字段的对象
          instancesData = res.data;
        } else {
          instancesData = [];
        }

        setInstances(instancesData);
        calculateStatistics(instancesData);
      }
    } catch (error) {
      message.error('获取文档实例失败：' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchTemplates = async () => {
    try {
      const res = await http.get('/api/model-storage/word-templates/');

      if (!res.error) {
        // 检查模板响应格式
        let templatesData = [];
        if (Array.isArray(res)) {
          // 直接是数组
          templatesData = res;
        } else if (res.data && Array.isArray(res.data)) {
          // 包含data字段的对象
          templatesData = res.data;
        } else {
          templatesData = [];
        }

        setTemplates(templatesData);
      }
    } catch (error) {
      console.error('获取模板列表失败:', error);
    }
  };

  const calculateStatistics = (data) => {
    if (!Array.isArray(data)) {
      data = [];
    }

    const stats = {
      total: data.length,
      draft: data.filter(d => d.status === 'draft').length,
      generated: data.filter(d => d.status === 'generated').length,
      exported: data.filter(d => d.status === 'exported').length
    };

    setStatistics(stats);
  };

  const handleDownload = async (record) => {
    if (!record.generated_file_path) {
      message.warning('文档尚未生成，无法下载');
      return;
    }

    try {
      const response = await fetch(`/api/model-storage/documents/download/?instance_id=${record.id}`, {
        method: 'GET',
        headers: {
          'X-Token': localStorage.getItem('token') || ''
        }
      });

      if (!response.ok) {
        throw new Error('下载失败');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${record.title}.docx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      message.success('文档下载成功');
      fetchInstances(); // 刷新列表以更新导出统计
    } catch (error) {
      message.error('下载文档失败：' + error.message);
    }
  };

  const handleDelete = async (id) => {
    try {
      const res = await http.delete('/api/model-storage/document-instances/', {
        data: { id }
      });
      if (res.error) {
        message.error(res.error);
      } else {
        message.success('文档实例删除成功');
        fetchInstances();
      }
    } catch (error) {
      message.error('删除文档实例失败：' + error.message);
    }
  };

  const handleRegenerate = async (record) => {
    try {
      const res = await http.post('/api/model-storage/documents/generate/', {
        instance_id: record.id
      });
      
      if (res.error) {
        message.error(res.error);
      } else {
        message.success('文档重新生成成功');
        fetchInstances();
      }
    } catch (error) {
      message.error('重新生成文档失败：' + error.message);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      draft: 'orange',
      generated: 'blue',
      exported: 'green',
      archived: 'gray'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      draft: '草稿',
      generated: '已生成',
      exported: '已导出',
      archived: '已归档'
    };
    return texts[status] || status;
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const columns = [
    {
      title: '文档标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (text, record) => (
        <Space>
          <FileWordOutlined style={{ color: '#1890ff' }} />
          <div>
            <Text strong>{text}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              v{record.version}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '关联模板',
      dataIndex: 'template_name',
      key: 'template_name',
      width: 150,
      render: (text) => (
        <Tag color="blue">{text}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      width: 100,
      render: (size) => (
        <Text type="secondary">
          {size > 0 ? formatFileSize(size) : '-'}
        </Text>
      ),
    },
    {
      title: '导出次数',
      dataIndex: 'export_count',
      key: 'export_count',
      width: 100,
      render: (count) => (
        <Text type="secondary">{count || 0}</Text>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time) => (
        <Text type="secondary">{time}</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          
          {record.status === 'generated' || record.status === 'exported' ? (
            <Tooltip title="下载文档">
              <Button 
                type="text" 
                icon={<DownloadOutlined />} 
                size="small"
                onClick={() => handleDownload(record)}
              />
            </Tooltip>
          ) : (
            <Tooltip title="重新生成">
              <Button 
                type="text" 
                icon={<ReloadOutlined />} 
                size="small"
                onClick={() => handleRegenerate(record)}
              />
            </Tooltip>
          )}
          
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          
          <Tooltip title="复制">
            <Button 
              type="text" 
              icon={<CopyOutlined />} 
              size="small"
              onClick={() => handleCopy(record)}
            />
          </Tooltip>
          
          <Popconfirm
            title="确定要删除这个文档实例吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="text" 
                icon={<DeleteOutlined />} 
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleViewDetails = (record) => {
    // TODO: 实现查看详情功能
    message.info('查看详情功能开发中...');
  };

  const handleEdit = (record) => {
    // TODO: 实现编辑功能
    message.info('编辑功能开发中...');
  };

  const handleCopy = async (record) => {
    // TODO: 实现复制功能
    message.info('复制功能开发中...');
  };

  return (
    <div className={styles.container}>
      <Card className={styles.headerCard}>
        <Row gutter={24}>
          <Col span={6}>
            <Statistic
              title="总文档数"
              value={statistics.total}
              prefix={<FileWordOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="草稿"
              value={statistics.draft}
              valueStyle={{ color: '#d48806' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="已生成"
              value={statistics.generated}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="已导出"
              value={statistics.exported}
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
        </Row>
      </Card>

      <Card 
        title={
          <Space>
            <FileWordOutlined />
            <Title level={4} style={{ margin: 0 }}>文档实例管理</Title>
          </Space>
        }
        extra={
          <Space>
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchInstances}
            >
              刷新
            </Button>
          </Space>
        }
      >
        {/* 筛选器 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Search
              placeholder="搜索文档标题"
              allowClear
              onSearch={(value) => setFilters({...filters, search: value})}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择模板"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setFilters({...filters, template_id: value})}
            >
              {templates.map(template => (
                <Option key={template.id} value={template.id}>
                  {template.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="选择状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setFilters({...filters, status: value})}
            >
              <Option value="draft">草稿</Option>
              <Option value="generated">已生成</Option>
              <Option value="exported">已导出</Option>
              <Option value="archived">已归档</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              style={{ width: '100%' }}
              onChange={(dates) => setFilters({...filters, date_range: dates})}
            />
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={instances}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>
    </div>
  );
});
