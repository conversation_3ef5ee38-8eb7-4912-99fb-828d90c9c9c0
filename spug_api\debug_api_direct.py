#!/usr/bin/env python3
"""
直接调试API
"""
import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.models import Detection
from django_redis import get_redis_connection

def debug_api_direct():
    """直接调试API"""
    print("=== 直接调试API ===\n")
    
    # 手动实现get_overview逻辑
    response = []
    rds = get_redis_connection()
    
    print("📊 开始处理监控任务...")
    
    for item in Detection.objects.all():
        print(f"\n处理监控任务: {item.name} (ID: {item.id})")
        print(f"  是否激活: {item.is_active}")
        print(f"  目标: {item.targets}")
        print(f"  最后运行: {item.latest_run_time}")
        
        data = {}
        
        try:
            targets = json.loads(item.targets)
            print(f"  解析后的目标: {targets}")
        except Exception as e:
            print(f"  目标解析失败: {e}")
            continue
            
        for key in targets:
            key = str(key)
            print(f"  处理目标: {key}")
            
            data[key] = {
                'id': f'{item.id}_{key}',
                'group': item.group,
                'name': item.name,
                'type': item.get_type_display(),
                'target': key,
                'desc': item.desc,
                'status': '0',
                'latest_run_time': item.latest_run_time,
                'process_info': None,
            }
            
            # 获取进程详细信息（仅对智能进程检测）
            if item.type == '6':
                process_info_key = f'spug:det:info:{item.id}_{key}'
                print(f"    查找进程信息: {process_info_key}")
                
                process_info_data = rds.get(process_info_key)
                if process_info_data:
                    try:
                        process_info = json.loads(process_info_data.decode())
                        data[key]['process_info'] = process_info
                        print(f"    ✅ 找到进程信息: {len(process_info)} 个进程")
                    except Exception as e:
                        print(f"    ❌ 进程信息解析失败: {e}")
                else:
                    print(f"    ⚠️ 没有找到进程信息")
            
            # 设置状态
            if item.is_active:
                if item.latest_run_time:
                    data[key]['status'] = '1'
                    print(f"    状态: 正常 (1)")
                else:
                    data[key]['status'] = '10'
                    print(f"    状态: 未运行 (10)")
            else:
                print(f"    监控任务未激活，跳过")
                continue
        
        # 检查Redis中的监控状态
        if item.is_active:
            monitor_key = f'spug:det:{item.id}'
            print(f"  检查监控状态: {monitor_key}")
            
            monitor_data = rds.hgetall(monitor_key)
            if monitor_data:
                print(f"    监控状态数据: {len(monitor_data)} 个字段")
                for k, v in monitor_data.items():
                    key_str = k.decode()
                    val_str = v.decode()
                    print(f"      {key_str}: {val_str}")
                    
                    if '_' in key_str:
                        prefix, target_key = key_str.split('_', 1)
                        if target_key in data:
                            val = int(val_str)
                            if prefix == 'c':
                                if data[target_key]['status'] == '1':
                                    data[target_key]['status'] = '2'
                                data[target_key]['count'] = val
                                print(f"        更新目标 {target_key} 状态为警告，错误计数: {val}")
                            elif prefix == 't':
                                from datetime import datetime
                                date = datetime.fromtimestamp(val).strftime('%Y-%m-%d %H:%M:%S')
                                data[target_key].update(status='3', notified_at=date)
                                print(f"        更新目标 {target_key} 状态为故障，通知时间: {date}")
            else:
                print(f"    没有监控状态数据")
        
        # 添加到响应
        valid_data = list(data.values())
        response.extend(valid_data)
        print(f"  添加了 {len(valid_data)} 个监控项到响应")
    
    print(f"\n📋 最终响应: {len(response)} 个监控项")
    for i, item in enumerate(response):
        print(f"  {i+1}. {item['name']}:")
        print(f"     ID: {item['id']}")
        print(f"     状态: {item['status']}")
        print(f"     进程信息: {'有' if item['process_info'] else '无'}")
        if item['process_info']:
            print(f"     进程数量: {len(item['process_info'])}")
    
    # 测试实际的API函数
    print(f"\n🌐 测试实际API函数...")
    from apps.monitor.views import get_overview
    from django.test import RequestFactory
    from apps.account.models import User
    
    factory = RequestFactory()
    request = factory.get('/api/monitor/overview/')
    request.user = User.objects.first()
    
    try:
        response_obj = get_overview(request)
        if hasattr(response_obj, 'content'):
            content = response_obj.content.decode()
            response_data = json.loads(content)
            
            print(f"API响应结构: {list(response_data.keys())}")
            if 'data' in response_data:
                api_data = response_data['data']
                print(f"API返回数据: {len(api_data)} 个监控项")
                
                for item in api_data:
                    print(f"  - {item.get('name')}: 状态={item.get('status')}, 进程信息={'有' if item.get('process_info') else '无'}")
            else:
                print(f"API响应格式错误: {response_data}")
        else:
            print(f"API调用失败")
    except Exception as e:
        print(f"API调用异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_api_direct()
