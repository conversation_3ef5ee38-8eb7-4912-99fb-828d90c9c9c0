#!/usr/bin/env python3
"""
测试SSH PATH
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.host.models import Host

def test_ssh_path():
    """测试SSH PATH"""
    print("=== 测试SSH PATH ===\n")
    
    # 获取主机
    host = Host.objects.get(pk=1)
    print(f"🖥️ 目标主机: {host.name} ({host.hostname})")
    
    try:
        with host.get_ssh() as ssh:
            # 检查PATH
            exit_code, out = ssh.exec_command_raw("echo $PATH")
            print(f"SSH PATH: {out}")
            
            # 检查which docker
            exit_code, out = ssh.exec_command_raw("which docker")
            print(f"which docker (退出码: {exit_code}): {out}")
            
            # 尝试使用完整路径
            exit_code, out = ssh.exec_command_raw("/usr/bin/docker ps --format '{{.Names}}'")
            print(f"/usr/bin/docker ps (退出码: {exit_code}): {out}")
            
            # 尝试设置PATH后执行
            exit_code, out = ssh.exec_command_raw("export PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin && docker ps --format '{{.Names}}'")
            print(f"设置PATH后docker ps (退出码: {exit_code}): {out}")
            
            # 测试进程搜索
            exit_code, out = ssh.exec_command_raw("/usr/bin/docker exec Qwen3 ps -eo pid,ppid,cmd,etime,pcpu,pmem --no-headers 2>/dev/null | grep -v grep | grep 'run_28k_4k_qwen2'")
            print(f"进程搜索 (退出码: {exit_code}): {out}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_ssh_path()
