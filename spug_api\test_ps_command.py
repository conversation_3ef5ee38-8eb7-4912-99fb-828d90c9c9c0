#!/usr/bin/env python3
"""
测试ps命令的输出格式
"""
import os
import sys
import django

# 设置Django环境
sys.path.insert(0, '/'.join(os.path.abspath(__file__).split('/')[:-1]))
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "spug.settings")
django.setup()

from apps.host.models import Host

def test_ps_command():
    print("=== 测试ps命令输出格式 ===\n")
    
    try:
        host = Host.objects.get(id=5)
        print(f"连接主机: {host.name} ({host.hostname})")
        
        with host.get_ssh() as ssh:
            # 测试ps命令
            command = "ps -eo pid,ppid,cmd,etime,pcpu,pmem --no-headers | grep -v grep | grep 'run_28k_4k_qwen2'"
            print(f"执行命令: {command}")
            
            exit_code, out = ssh.exec_command_raw(command)
            print(f"退出码: {exit_code}")
            print(f"输出内容:")
            print("=" * 50)
            print(out)
            print("=" * 50)
            
            if out.strip():
                lines = out.strip().split('\n')
                for i, line in enumerate(lines):
                    print(f"\n行 {i+1}: '{line}'")
                    parts = line.strip().split(None, 5)
                    print(f"分割后的部分 ({len(parts)} 个):")
                    for j, part in enumerate(parts):
                        print(f"  [{j}]: '{part}'")
            else:
                print("没有找到匹配的进程")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_ps_command()
