#!/usr/bin/env python3
"""
测试运行时间格式化功能
"""

def formatRuntime(etime):
    """格式化运行时间显示"""
    if not etime or etime == '0':
        return '未运行'

    print(f"原始时间格式: {etime}")

    # 处理格式如 "2-15:30:45" (天-小时:分钟:秒) - 先处理这个格式
    if etime.count('-') == 1 and ':' in etime:
        days_part, time_part = etime.split('-')
        days = int(days_part)
        hours, minutes = time_part.split(':')[:2]
        hours, minutes = int(hours), int(minutes)

        if days > 0:
            result = f"{days}天{hours}小时"
        elif hours > 0:
            result = f"{hours}小时{minutes}分钟"
        else:
            result = f"{minutes}分钟"

        print(f"格式化结果: {result}")
        return result

    # 处理格式如 "05:04:25" (小时:分钟:秒)
    if etime.count(':') == 2 and '-' not in etime:
        parts = etime.split(':')
        hours = int(parts[0])
        minutes = int(parts[1])

        if hours > 0:
            result = f"{hours}小时{minutes}分钟"
        elif minutes > 0:
            result = f"{minutes}分钟"
        else:
            result = '刚启动'

        print(f"格式化结果: {result}")
        return result

    print(f"未知格式，返回原值: {etime}")
    return etime

def test_runtime_formats():
    """测试各种运行时间格式"""
    print("=== 运行时间格式化测试 ===\n")
    
    test_cases = [
        "05:04:25",      # 5小时4分25秒
        "00:30:15",      # 30分15秒
        "12:00:00",      # 12小时
        "2-15:30:45",    # 2天15小时30分45秒
        "0-08:22:15",    # 8小时22分15秒
        "1-00:00:00",    # 1天
        "00:00:30",      # 30秒
        "0",             # 未运行
        "",              # 空值
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"测试用例 {i}:")
        formatRuntime(test_case)
        print()

if __name__ == '__main__':
    test_runtime_formats()
