import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Card,
  Row,
  Col,
  Typography,
  Divider,
  Space,
  Tag
} from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import http from '../../../libs/http';
import SimpleRichTextEditor from './SimpleRichTextEditor';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

export default function VariableForm({ form, variables }) {
  const [gpuOptions, setGpuOptions] = useState([]);
  const [vendorOptions, setVendorOptions] = useState([]);

  useEffect(() => {
    // 获取GPU选项
    fetchGpuOptions();
    // 获取厂商选项
    fetchVendorOptions();
  }, []);

  const fetchGpuOptions = async () => {
    try {
      const res = await http.get('/api/model-storage/gpus/');
      if (!res.error && res.data) {
        const options = res.data.map(gpu => ({
          label: `${gpu.name} (${gpu.vendor})`,
          value: gpu.name
        }));
        setGpuOptions(options);
      }
    } catch (error) {
      console.error('获取GPU选项失败:', error);
    }
  };

  const fetchVendorOptions = async () => {
    try {
      const res = await http.get('/api/model-storage/gpus/');
      if (!res.error && res.data) {
        const vendors = [...new Set(res.data.map(gpu => gpu.vendor))];
        const options = vendors.map(vendor => ({
          label: vendor,
          value: vendor
        }));
        setVendorOptions(options);
      }
    } catch (error) {
      console.error('获取厂商选项失败:', error);
    }
  };

  const renderFormItem = (variable) => {
    const commonProps = {
      name: variable.variable_name,
      label: (
        <Space>
          {variable.display_name}
          {variable.is_required && <Text type="danger">*</Text>}
          {variable.help_text && (
            <Text type="secondary" style={{ fontSize: '12px' }}>
              <InfoCircleOutlined style={{ marginRight: 4 }} />
              {variable.help_text}
            </Text>
          )}
        </Space>
      ),
      rules: variable.is_required ? [
        { required: true, message: `请输入${variable.display_name}` }
      ] : []
    };

    switch (variable.variable_type) {
      case 'text':
        return (
          <Form.Item key={variable.id} {...commonProps}>
            <Input 
              placeholder={variable.placeholder || `请输入${variable.display_name}`}
              size="large"
            />
          </Form.Item>
        );

      case 'textarea':
        return (
          <Form.Item key={variable.id} {...commonProps}>
            <TextArea
              rows={3}
              placeholder={variable.placeholder || `请输入${variable.display_name}`}
            />
          </Form.Item>
        );

      case 'richtext':
        return (
          <Form.Item key={variable.id} {...commonProps}>
            <SimpleRichTextEditor
              placeholder={variable.placeholder || `请输入${variable.display_name}`}
              height={300}
            />
          </Form.Item>
        );

      case 'select':
        const selectOptions = variable.options || [];
        return (
          <Form.Item key={variable.id} {...commonProps}>
            <Select 
              placeholder={variable.placeholder || `请选择${variable.display_name}`}
              size="large"
            >
              {selectOptions.map(option => (
                <Option key={option} value={option}>
                  {option}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      case 'multiselect':
        const multiOptions = variable.options || [];
        return (
          <Form.Item key={variable.id} {...commonProps}>
            <Select 
              mode="multiple"
              placeholder={variable.placeholder || `请选择${variable.display_name}`}
              size="large"
            >
              {multiOptions.map(option => (
                <Option key={option} value={option}>
                  {option}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      case 'date':
        return (
          <Form.Item key={variable.id} {...commonProps}>
            <DatePicker 
              placeholder={variable.placeholder || `请选择${variable.display_name}`}
              size="large"
              style={{ width: '100%' }}
            />
          </Form.Item>
        );

      case 'number':
        return (
          <Form.Item key={variable.id} {...commonProps}>
            <InputNumber 
              placeholder={variable.placeholder || `请输入${variable.display_name}`}
              size="large"
              style={{ width: '100%' }}
            />
          </Form.Item>
        );

      case 'gpu_model':
        return (
          <Form.Item key={variable.id} {...commonProps}>
            <Select 
              placeholder={variable.placeholder || `请选择${variable.display_name}`}
              size="large"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {gpuOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      case 'vendor':
        return (
          <Form.Item key={variable.id} {...commonProps}>
            <Select 
              placeholder={variable.placeholder || `请选择${variable.display_name}`}
              size="large"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {vendorOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      default:
        return (
          <Form.Item key={variable.id} {...commonProps}>
            <Input 
              placeholder={variable.placeholder || `请输入${variable.display_name}`}
              size="large"
            />
          </Form.Item>
        );
    }
  };

  // 按组分组变量
  const groupedVariables = variables.reduce((groups, variable) => {
    const group = variable.group_name || '其他';
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(variable);
    return groups;
  }, {});

  // 按sort_order排序每个组内的变量
  Object.keys(groupedVariables).forEach(group => {
    groupedVariables[group].sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
  });

  return (
    <Form form={form} layout="vertical">
      {Object.keys(groupedVariables).map((groupName, groupIndex) => (
        <div key={groupName}>
          {groupIndex > 0 && <Divider />}
          
          <Title level={4} style={{ marginBottom: 16 }}>
            {groupName}
            <Tag color="blue" style={{ marginLeft: 8 }}>
              {groupedVariables[groupName].length} 个变量
            </Tag>
          </Title>

          <Row gutter={[16, 0]}>
            {groupedVariables[groupName].map(variable => (
              <Col
                key={variable.id}
                xs={24}
                sm={24}
                md={(variable.variable_type === 'textarea' || variable.variable_type === 'richtext') ? 24 : 12}
                lg={(variable.variable_type === 'textarea' || variable.variable_type === 'richtext') ? 24 : 12}
              >
                {renderFormItem(variable)}
              </Col>
            ))}
          </Row>
        </div>
      ))}
    </Form>
  );
}
