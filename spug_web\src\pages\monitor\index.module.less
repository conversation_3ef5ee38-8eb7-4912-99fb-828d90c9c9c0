.steps {
    width: 520px;
    margin: 0 auto 30px;
}

.card {
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 60px;
    min-height: 40px;
    font-size: 10px;
    color: inherit;
    border-radius: 4px;
    padding: 4px;
    text-align: center;
    line-height: 1.2;
    word-break: break-all;
    cursor: pointer;

    &:hover {
        opacity: 0.8;
    }
}

.processCard {
    width: 200px;
    height: 100px;
    margin: 8px;
    padding: 16px;
    background: #ffffff;
    border: 2px solid #e8e8e8;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        border-color: #1890ff;
    }

    .processTitle {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        line-height: 1.4;
        margin-bottom: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .processStatus {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .statusRow {
            display: flex;
            align-items: center;
            gap: 8px;

            .statusDot {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                flex-shrink: 0;

                &.running {
                    background-color: #52c41a;
                    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
                }

                &.stopped {
                    background-color: #ff4d4f;
                    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
                }
            }

            .statusText {
                font-size: 14px;
                font-weight: 500;
                color: #595959;
            }
        }

        .runtimeRow {
            margin-left: 18px;

            .runtimeText {
                font-size: 13px;
                font-weight: 600;
                color: #1890ff;
                font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            }
        }
    }

    // 运行中的卡片样式
    &.running {
        border-color: #52c41a;
        background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);

        &:hover {
            border-color: #389e0d;
        }
    }

    // 未运行的卡片样式
    &.stopped {
        border-color: #ff4d4f;
        background: linear-gradient(135deg, #fff2f0 0%, #ffffff 100%);

        &:hover {
            border-color: #cf1322;
        }

        .runtimeText {
            color: #8c8c8c;
        }
    }
}

.header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 12px;
    margin-top: -6px;

    .item {
        display: flex;
        justify-content: center;
        align-items: center;
        min-width: 26px;
        height: 20px;
        margin-left: 12px;
        border-radius: 10px;
        padding: 0 8px;
        color: #fff;
        font-weight: bold;
        cursor: pointer;
    }

    .autoLoad {
        margin-left: 24px;
        font-size: 18px;
        color: #999999;
    }
}

.cardGrid {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 16px;
    justify-content: flex-start;

    @media (max-width: 1200px) {
        justify-content: center;
    }
}

.notMatch {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
    padding: 40px 0;
    font-size: 14px;

    :global(.anticon) {
        font-size: 18px;
        margin-right: 8px;
    }
}

