/**
 * 测试用例集编辑器
 */
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Tabs,
  Table,
  Space,
  Popconfirm,
  message,
  Breadcrumb,
  Alert,
  Row,
  Col,
  Typography,
  Switch,
  Tooltip,
  Modal,
  Tag,
  Spin
} from 'antd';
import {
  SaveOutlined,
  ArrowLeftOutlined,
  PlusOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  FileTextOutlined,
  HolderOutlined,
  EditOutlined,
  ImportOutlined
} from '@ant-design/icons';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import { http, history } from 'libs';
import styles from './TestCaseSetEditor.module.css';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 可编辑单元格组件
const EditableCell = React.memo(({ value, onChange, placeholder, type = 'input', rows = 1 }) => {
  const [editing, setEditing] = useState(false);
  const [inputValue, setInputValue] = useState(value || '');
  const inputRef = useRef(null);

  useEffect(() => {
    setInputValue(value || '');
  }, [value]);

  useEffect(() => {
    if (editing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [editing]);

  const handleSave = useCallback(() => {
    setEditing(false);
    if (inputValue !== value) {
      onChange(inputValue);
    }
  }, [inputValue, value, onChange]);

  const handleCancel = useCallback(() => {
    setEditing(false);
    setInputValue(value || '');
  }, [value]);

  const handleKeyPress = useCallback((e) => {
    if (e.key === 'Enter' && !e.shiftKey && type === 'input') {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  }, [handleSave, handleCancel, type]);

  if (editing) {
    const InputComponent = type === 'textarea' ? Input.TextArea : Input;
    return (
      <InputComponent
        ref={inputRef}
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onBlur={handleSave}
        onKeyDown={handleKeyPress}
        placeholder={placeholder}
        rows={type === 'textarea' ? rows : undefined}
        style={{ margin: '-5px -11px' }}
        autoSize={type === 'textarea' ? { minRows: rows, maxRows: 6 } : undefined}
      />
    );
  }

  return (
    <div
      onClick={() => setEditing(true)}
      style={{
        minHeight: type === 'textarea' ? `${rows * 22 + 10}px` : '32px',
        padding: '4px 8px',
        cursor: 'text',
        border: '1px solid transparent',
        borderRadius: '4px',
        whiteSpace: type === 'textarea' ? 'pre-wrap' : 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        lineHeight: '1.5',
        backgroundColor: 'transparent',
        transition: 'all 0.2s'
      }}
      onMouseEnter={(e) => {
        e.target.style.backgroundColor = '#f5f5f5';
        e.target.style.border = '1px solid #d9d9d9';
      }}
      onMouseLeave={(e) => {
        e.target.style.backgroundColor = 'transparent';
        e.target.style.border = '1px solid transparent';
      }}
      title={value || placeholder}
    >
      {value || <span style={{ color: '#bfbfbf' }}>{placeholder}</span>}
    </div>
  );
});

// 拖拽手柄组件
const DragHandle = SortableHandle(() => (
  <HolderOutlined className={styles.dragHandle} />
));

// 可排序的表格行
const SortableItem = SortableElement(props => <tr {...props} />);

// 可排序的表格容器
const SortableBody = SortableContainer(props => <tbody {...props} />);

function TestCaseSetEditor({ match }) {
  const [testCaseSet, setTestCaseSet] = useState(null);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [testCases, setTestCases] = useState([]);
  const [activeTab, setActiveTab] = useState('basic');
  const isMountedRef = useRef(true);

  // 用例模板选择相关状态
  const [caseTemplateSelectorVisible, setCaseTemplateSelectorVisible] = useState(false);
  const [caseTemplates, setCaseTemplates] = useState([]);
  const [caseTemplateLoading, setCaseTemplateLoading] = useState(false);

  const caseSetId = match.params.id;
  const isEdit = caseSetId && caseSetId !== 'new';

  useEffect(() => {
    isMountedRef.current = true;
    
    if (isEdit) {
      fetchTestCaseSet();
    } else {
      // 新建模式，初始化默认数据
      const defaultCases = [
        {
          id: Date.now(),
          name: '测试用例1',
          precondition: '系统正常运行',
          test_steps: '1. 打开应用\n2. 执行操作\n3. 验证结果',
          expected_result: '操作成功，结果符合预期',
          enabled: true,
          sort_order: 1
        }
      ];
      
      const newCaseSet = {
        name: '',
        description: '',
        category: '',
        test_cases: defaultCases
      };
      setTestCaseSet(newCaseSet);
      setTestCases(defaultCases);
      form.setFieldsValue({
        name: newCaseSet.name,
        description: newCaseSet.description,
        category: newCaseSet.category
      });
    }

    return () => {
      isMountedRef.current = false;
    };
  }, [caseSetId]);

  const fetchTestCaseSet = async () => {
    if (!isMountedRef.current) return;
    setLoading(true);
    try {
      const response = await http.get(`/api/exec/test-case-sets/${caseSetId}/`);
      const caseSet = response.data || response;
      console.log('加载的测试用例集数据:', caseSet);
      
      if (!isMountedRef.current) return;
      setTestCaseSet(caseSet);
      
      const cases = caseSet.test_cases || [];
      setTestCases(cases);
      
      form.setFieldsValue({
        name: caseSet.name,
        description: caseSet.description,
        category: caseSet.category
      });
    } catch (error) {
      if (isMountedRef.current) {
        message.error('加载测试用例集失败');
      }
      console.error('Fetch test case set error:', error);
    } finally {
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      const caseSetData = {
        ...values,
        test_cases: testCases
      };

      console.log('保存的测试用例集数据:', caseSetData);

      if (isEdit) {
        await http.put(`/api/exec/test-case-sets/${caseSetId}/`, caseSetData);
        message.success('测试用例集更新成功');
      } else {
        await http.post('/api/exec/test-case-sets/', caseSetData);
        message.success('测试用例集创建成功');
      }
      
      handleBack();
    } catch (error) {
      message.error('保存失败');
      console.error('Save test case set error:', error);
    }
  };

  const handleBack = () => {
    history.push('/exec/test-case-sets');
  };

  const handleAddTestCase = () => {
    const newCase = {
      id: Date.now(),
      name: `测试用例${testCases.length + 1}`,
      precondition: '',
      test_steps: '',
      expected_result: '',
      enabled: true,
      sort_order: testCases.length + 1
    };
    setTestCases([...testCases, newCase]);
  };

  // 用例模板管理函数
  const loadCaseTemplates = async () => {
    if (!isMountedRef.current) return;
    setCaseTemplateLoading(true);
    try {
      const response = await http.get('/api/exec/test-case-templates/');
      if (!isMountedRef.current) return;
      setCaseTemplates(response || []);
    } catch (error) {
      if (isMountedRef.current) {
        message.error('加载用例模板失败');
      }
      console.error('Load case templates error:', error);
    } finally {
      if (isMountedRef.current) {
        setCaseTemplateLoading(false);
      }
    }
  };

  const handleImportCaseTemplate = (caseTemplate) => {
    // 检查是否已经引用了这个模板
    const existingTemplateCase = testCases.find(c =>
      c.is_template_reference && c.template_id === caseTemplate.id
    );

    if (existingTemplateCase) {
      message.warning(`模板"${caseTemplate.name}"已经被引用，无需重复添加`);
      return;
    }

    // 创建模板引用（不是复制内容）
    const templateReference = {
      id: `template_${caseTemplate.id}`,  // 使用特殊ID标识这是模板引用
      name: caseTemplate.name,
      precondition: caseTemplate.precondition || '',
      test_steps: caseTemplate.test_steps || '',
      expected_result: caseTemplate.expected_result || '',
      enabled: caseTemplate.enabled !== false,
      sort_order: testCases.length + 1,
      is_template_reference: true,  // 标记为模板引用
      template_id: caseTemplate.id,  // 保存原始模板ID
      template_category: caseTemplate.category,
      template_description: caseTemplate.description
    };

    message.success(`已引用用例模板"${caseTemplate.name}"，模板更新时此用例会自动同步`);
    setTestCases([...testCases, templateReference]);
    setCaseTemplateSelectorVisible(false);
  };

  const handleDeleteTestCase = (caseId) => {
    setTestCases(testCases.filter(c => c.id !== caseId));
  };

  const handleTestCaseChange = useCallback((caseId, field, value) => {
    setTestCases(prevCases => prevCases.map(c =>
      c.id === caseId ? { ...c, [field]: value } : c
    ));
  }, []);

  const handleSortEnd = ({ oldIndex, newIndex }) => {
    if (oldIndex === newIndex) return;
    
    const newCases = [...testCases];
    const [removed] = newCases.splice(oldIndex, 1);
    newCases.splice(newIndex, 0, removed);
    
    // 更新排序
    const updatedCases = newCases.map((c, index) => ({
      ...c,
      sort_order: index + 1
    }));
    
    setTestCases(updatedCases);
  };

  const columns = [
    {
      title: '排序',
      dataIndex: 'sort',
      width: 60,
      render: () => <DragHandle />,
    },
    {
      title: '用例名称',
      dataIndex: 'name',
      width: 200,
      render: (text, record) => {
        if (record.is_template_reference) {
          // 模板引用用例，显示为只读并添加标识
          return (
            <div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Tag color="blue" size="small" style={{ marginRight: '8px' }}>
                  模板引用
                </Tag>
                <span style={{ fontWeight: 500 }}>{text}</span>
              </div>
              {record.template_description && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                  {record.template_description}
                </div>
              )}
            </div>
          );
        }
        return (
          <EditableCell
            value={text}
            onChange={(value) => handleTestCaseChange(record.id, 'name', value)}
            placeholder="请输入用例名称"
            type="input"
          />
        );
      },
    },
    {
      title: '预置条件',
      dataIndex: 'precondition',
      width: 250,
      render: (text, record) => {
        if (record.is_template_reference) {
          return (
            <div style={{
              padding: '4px 8px',
              backgroundColor: '#f5f5f5',
              borderRadius: '4px',
              minHeight: '50px',
              whiteSpace: 'pre-wrap'
            }}>
              {text || '无'}
            </div>
          );
        }
        return (
          <EditableCell
            value={text}
            onChange={(value) => handleTestCaseChange(record.id, 'precondition', value)}
            placeholder="请输入预置条件"
            type="textarea"
            rows={2}
          />
        );
      },
    },
    {
      title: '测试步骤',
      dataIndex: 'test_steps',
      width: 300,
      render: (text, record) => {
        if (record.is_template_reference) {
          return (
            <div style={{
              padding: '4px 8px',
              backgroundColor: '#f5f5f5',
              borderRadius: '4px',
              minHeight: '70px',
              whiteSpace: 'pre-wrap'
            }}>
              {text || '无'}
            </div>
          );
        }
        return (
          <EditableCell
            value={text}
            onChange={(value) => handleTestCaseChange(record.id, 'test_steps', value)}
            placeholder="请输入测试步骤"
            type="textarea"
            rows={3}
          />
        );
      },
    },
    {
      title: '预期结果',
      dataIndex: 'expected_result',
      width: 250,
      render: (text, record) => {
        if (record.is_template_reference) {
          return (
            <div style={{
              padding: '4px 8px',
              backgroundColor: '#f5f5f5',
              borderRadius: '4px',
              minHeight: '50px',
              whiteSpace: 'pre-wrap'
            }}>
              {text || '无'}
            </div>
          );
        }
        return (
          <EditableCell
            value={text}
            onChange={(value) => handleTestCaseChange(record.id, 'expected_result', value)}
            placeholder="请输入预期结果"
            type="textarea"
            rows={2}
          />
        );
      },
    },
    {
      title: '启用',
      dataIndex: 'enabled',
      width: 80,
      render: (enabled, record) => {
        if (record.is_template_reference) {
          return (
            <Tag color={enabled ? 'green' : 'red'}>
              {enabled ? '启用' : '禁用'}
            </Tag>
          );
        }
        return (
          <Switch
            checked={enabled}
            onChange={(checked) => handleTestCaseChange(record.id, 'enabled', checked)}
          />
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space>
          {record.is_template_reference && (
            <Tooltip title="跳转到用例池编辑此模板">
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={() => {
                  // 跳转到用例池编辑页面
                  window.open(`/exec/test-case-templates`, '_blank');
                }}
              />
            </Tooltip>
          )}
          <Popconfirm
            title={record.is_template_reference ? "确定要移除这个模板引用吗？" : "确定要删除这个测试用例吗？"}
            onConfirm={() => handleDeleteTestCase(record.id)}
            okText={record.is_template_reference ? "移除" : "删除"}
            cancelText="取消"
          >
            <Tooltip title={record.is_template_reference ? "移除模板引用" : "删除测试用例"}>
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const SortableTable = (props) => {
    const { children, ...restProps } = props;
    return <SortableBody {...restProps} onSortEnd={handleSortEnd} useDragHandle>{children}</SortableBody>;
  };

  const SortableRow = (props) => {
    const { children, ...restProps } = props;
    const index = testCases.findIndex(x => x.id === restProps['data-row-key']);
    return <SortableItem index={index} {...restProps}>{children}</SortableItem>;
  };

  if (loading) {
    return <div>加载中...</div>;
  }

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: 'calc(100vh - 64px)' }}>
      {/* 面包屑导航 */}
      <Breadcrumb style={{ marginBottom: '16px' }}>
        <Breadcrumb.Item>脚本配置中心</Breadcrumb.Item>
        <Breadcrumb.Item>
          <a onClick={handleBack} style={{ cursor: 'pointer' }}>测试用例集</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>{isEdit ? '编辑' : '新建'}</Breadcrumb.Item>
      </Breadcrumb>

      {/* 标题和操作按钮 */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        marginBottom: '24px',
        background: 'white',
        padding: '16px 24px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <Title level={3} style={{ margin: 0 }}>
          {isEdit ? `编辑测试用例集: ${testCaseSet?.name || ''}` : '新建测试用例集'}
        </Title>
        <Space>
          <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
            返回
          </Button>
          <Button type="primary" icon={<SaveOutlined />} onClick={handleSave}>
            保存用例集
          </Button>
        </Space>
      </div>

      {/* 主要内容 */}
      <Card style={{ borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<span><InfoCircleOutlined />基本信息</span>} key="basic">
            <Row gutter={24}>
              <Col span={12}>
                <Form form={form} layout="vertical">
                  <Form.Item
                    name="name"
                    label="测试用例集名称"
                    rules={[{ required: true, message: '请输入测试用例集名称' }]}
                  >
                    <Input placeholder="例如: 用户登录功能测试用例集" />
                  </Form.Item>
                  
                  <Form.Item
                    name="category"
                    label="分类"
                  >
                    <Input placeholder="例如: functional, performance, security" />
                  </Form.Item>
                  
                  <Form.Item
                    name="description"
                    label="描述"
                  >
                    <Input.TextArea 
                      rows={4} 
                      placeholder="描述这个测试用例集的用途和范围..."
                    />
                  </Form.Item>
                </Form>
              </Col>
              <Col span={12}>
                <Alert
                  message="测试用例集说明"
                  description={
                    <div>
                      <p><strong>用例集结构：</strong></p>
                      <ul>
                        <li>📝 预置条件：执行测试前需要满足的条件</li>
                        <li>🔄 测试步骤：详细的操作步骤</li>
                        <li>✅ 预期结果：期望的测试结果</li>
                      </ul>
                      <p><strong>统计信息：</strong></p>
                      <ul>
                        <li>📋 测试用例: {testCases.length} 个 (启用: {testCases.filter(c => c.enabled).length} 个)</li>
                      </ul>
                    </div>
                  }
                  type="info"
                  showIcon
                />
              </Col>
            </Row>
          </TabPane>

          <TabPane tab={<span><FileTextOutlined />测试用例 ({testCases.length})</span>} key="cases">
            <div style={{ marginBottom: '16px' }}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAddTestCase}
                >
                  📝 添加用例
                </Button>
                <Button
                  icon={<ImportOutlined />}
                  onClick={() => {
                    setCaseTemplateSelectorVisible(true);
                    loadCaseTemplates();
                  }}
                >
                  📋 引入用例模板
                </Button>
                <Text type="secondary">
                  共 {testCases.length} 个用例（自定义 {testCases.filter(c => !c.is_template_reference).length} 个，模板引用 {testCases.filter(c => c.is_template_reference).length} 个），启用 {testCases.filter(c => c.enabled).length} 个
                </Text>
              </Space>
              <Alert
                message="用例说明"
                description={
                  <div>
                    <p>每个测试用例包含预置条件、测试步骤和预期结果三个部分。</p>
                    <p><strong>🔄 拖拽排序：</strong>点击并拖拽 <HolderOutlined style={{ color: '#1890ff' }} /> 图标可以调整用例执行顺序</p>
                    <p><strong>📋 模板引用：</strong>带有"模板引用"标签的用例来自用例池，当原始模板更新时会自动同步</p>
                    <p><strong>✏️ 编辑模板：</strong>点击模板引用用例的编辑按钮可跳转到用例池进行编辑</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginTop: '16px' }}
              />
            </div>

            <Table
              dataSource={testCases}
              columns={columns}
              rowKey="id"
              pagination={false}
              scroll={{ x: 1200 }}
              components={{
                body: {
                  wrapper: SortableTable,
                  row: SortableRow,
                },
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 用例模板选择器 */}
      <Modal
        title="选择用例模板"
        visible={caseTemplateSelectorVisible}
        onCancel={() => setCaseTemplateSelectorVisible(false)}
        footer={null}
        width={1000}
        style={{ top: 20 }}
        bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}
      >
        <div style={{ marginBottom: 16 }}>
          <Alert
            message="用例模板导入说明"
            description={
              <div>
                <p><strong>📝 模板转换：</strong>用例模板将直接转换为测试用例，包含预置条件、测试步骤和预期结果</p>
                <p><strong>🔄 字段映射：</strong>模板的所有字段将完整保留到新的测试用例中</p>
                <p><strong>💡 使用建议：</strong>导入后可以根据具体需求修改用例内容</p>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        </div>

        <Spin spinning={caseTemplateLoading}>
          {caseTemplates.length === 0 && !caseTemplateLoading ? (
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>📝</div>
              <div style={{ fontSize: '16px', marginBottom: '8px' }}>暂无用例模板</div>
              <div style={{ fontSize: '14px' }}>请先在用例池中创建用例模板</div>
            </div>
          ) : (
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))', gap: '16px' }}>
              {caseTemplates.map(caseTemplate => (
                <Card
                  key={caseTemplate.id}
                  size="small"
                  hoverable
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleImportCaseTemplate(caseTemplate)}
                  title={
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <FileTextOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
                        <span style={{ fontWeight: 'bold' }}>{caseTemplate.name}</span>
                      </div>
                      <div>
                        {caseTemplate.category && (
                          <Tag color="blue" style={{ fontSize: '11px' }}>
                            {caseTemplate.category}
                          </Tag>
                        )}
                        <Tag color={caseTemplate.enabled ? 'green' : 'red'} style={{ fontSize: '11px' }}>
                          {caseTemplate.enabled ? '启用' : '禁用'}
                        </Tag>
                      </div>
                    </div>
                  }
                >
                  <div style={{ marginBottom: '12px' }}>
                    {caseTemplate.description ? (
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {caseTemplate.description}
                      </Text>
                    ) : (
                      <Text type="secondary" style={{ fontSize: '12px', fontStyle: 'italic' }}>
                        无描述信息
                      </Text>
                    )}
                  </div>

                  <div style={{ marginBottom: '8px' }}>
                    <Text strong style={{ fontSize: '12px', color: '#666' }}>预置条件：</Text>
                    <div style={{
                      fontSize: '12px',
                      color: '#333',
                      background: '#f5f5f5',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      marginTop: '4px',
                      maxHeight: '40px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>
                      {caseTemplate.precondition || '无'}
                    </div>
                  </div>

                  <div style={{ marginBottom: '8px' }}>
                    <Text strong style={{ fontSize: '12px', color: '#666' }}>测试步骤：</Text>
                    <div style={{
                      fontSize: '12px',
                      color: '#333',
                      background: '#f5f5f5',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      marginTop: '4px',
                      maxHeight: '60px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'pre-wrap'
                    }}>
                      {caseTemplate.test_steps || '无'}
                    </div>
                  </div>

                  <div style={{ marginBottom: '12px' }}>
                    <Text strong style={{ fontSize: '12px', color: '#666' }}>预期结果：</Text>
                    <div style={{
                      fontSize: '12px',
                      color: '#333',
                      background: '#f5f5f5',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      marginTop: '4px',
                      maxHeight: '40px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>
                      {caseTemplate.expected_result || '无'}
                    </div>
                  </div>

                  <div style={{ textAlign: 'center', paddingTop: '8px', borderTop: '1px solid #f0f0f0' }}>
                    <Button type="primary" size="small" style={{ fontSize: '12px' }}>
                      点击导入此用例模板
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </Spin>
      </Modal>
    </div>
  );
}

export default TestCaseSetEditor;
