/**
 * 智能结果收集页面
 */
import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { 
  Layout, 
  Card, 
  Button, 
  Space, 
  Tag, 
  Progress, 
  message, 
  Tooltip,
  Alert,
  Spin,
  Modal
} from 'antd';
import { 
  ExperimentOutlined, 
  RobotOutlined, 
  CheckCircleOutlined,
  CloseCircleOutlined,
  EyeOutlined,
  DownloadOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { http } from 'libs';
import LogViewer from './LogViewer';
import MetricsPanel from './MetricsPanel';
import TemplateSelector from './TemplateSelector';
import LogExtractionConfig from './LogExtractionConfig';
import styles from './index.module.less';

const { Header, Content, Sider } = Layout;

function ResultExtraction() {
  const [loading, setLoading] = useState(true);
  const [logContent, setLogContent] = useState('');
  const [executionInfo, setExecutionInfo] = useState({});
  const [extractedMetrics, setExtractedMetrics] = useState([]);
  const [selectedLines, setSelectedLines] = useState([]);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [extractionProgress, setExtractionProgress] = useState(0);
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [saving, setSaving] = useState(false);
  const [extractionConfigVisible, setExtractionConfigVisible] = useState(false);
  const [multiFileResults, setMultiFileResults] = useState([]);

  // 从URL参数获取执行记录信息或远程日志提取配置
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const executionId = urlParams.get('execution_id');
    const planName = urlParams.get('plan_name');
    const logFile = urlParams.get('log_file');

    // 检查是否来自远程日志提取页面
    const hostId = urlParams.get('hostId');
    const logPath = urlParams.get('logPath');
    const useDocker = urlParams.get('useDocker') === '1';
    const containerName = urlParams.get('containerName');
    const extractionMode = urlParams.get('extractionMode');
    const testPlanName = urlParams.get('testPlanName');

    if (hostId && logPath) {
      // 来自远程日志提取页面，自动打开配置弹窗
      setExecutionInfo({
        testPlanName: testPlanName || '远程日志分析',
        logFile: logPath,
        executionTime: new Date().toLocaleString(),
        hostId: hostId,
        useDocker: useDocker,
        containerName: containerName,
        extractionMode: extractionMode
      });

      // 自动打开配置弹窗
      setTimeout(() => {
        setExtractionConfigVisible(true);
      }, 500);

      setLoading(false);
    } else if (executionId && planName && logFile) {
      setExecutionInfo({
        id: executionId, // 使用真实的execution_id
        planName: decodeURIComponent(planName),
        logFile: decodeURIComponent(logFile)
      });
      
      loadLogContent(logFile);
    } else {
      message.error('缺少必要的参数');
      setLoading(false);
    }
  }, []);

  const loadLogContent = async (logFile) => {
    try {
      setLoading(true);
      const res = await http.get('/api/file/manager/edit/', { 
        params: { filename: logFile } 
      });
      
      const content = res.content || '日志文件为空';
      setLogContent(content);
      
      // 自动进行AI分析
      await performAIAnalysis(content);
      
    } catch (err) {
      message.error('读取日志文件失败: ' + (err.response?.data?.error || err.message));
      setLogContent('读取日志文件失败');
    } finally {
      setLoading(false);
    }
  };

  const performAIAnalysis = async (content) => {
    try {
      // 模拟AI分析过程
      setExtractionProgress(20);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 模拟识别性能指标
      const suggestions = analyzeLogContent(content);
      setAiSuggestions(suggestions);
      setExtractionProgress(60);
      
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 生成初始提取的指标
      const initialMetrics = suggestions.map(suggestion => ({
        id: Date.now() + Math.random(),
        label: suggestion.label,
        value: suggestion.value,
        unit: suggestion.unit,
        confidence: suggestion.confidence,
        status: suggestion.confidence > 0.8 ? 'confirmed' : 'pending',
        lineNumbers: suggestion.lineNumbers,
        category: getMetricCategory(suggestion.label, suggestion.unit),
        originalText: suggestion.originalText
      }));
      
      setExtractedMetrics(initialMetrics);
      setExtractionProgress(100);
      
      message.success(`AI分析完成，发现 ${suggestions.length} 个潜在指标`);
      
    } catch (err) {
      message.error('AI分析失败: ' + err.message);
      setExtractionProgress(0);
    }
  };

  // 增强的日志内容分析函数 - 支持多种格式识别
  const analyzeLogContent = (content) => {
    const suggestions = [];
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      // 跳过空行和分隔线
      if (!line.trim() || line.match(/^[-=]+$/)) return;

      // 模式1: 标准冒号格式 (如: Benchmark duration (s): 103.63)
      const pattern1 = /([^:]+):\s*([0-9.,]+)\s*$/gi;
      let match1;
      while ((match1 = pattern1.exec(line)) !== null) {
        const label = match1[1].trim();
        const value = match1[2].replace(',', '');

        if (parseFloat(value) > 0) {
          const unit = inferUnitFromLabel(label);
          suggestions.push({
            label: cleanLabel(label),
            value: value,
            unit: unit,
            confidence: 0.95,
            lineNumbers: [index + 1],
            originalText: match1[0]
          });
        }
      }

      // 模式2: 冒号格式 + 数据 + 单位 (如: AverageUserSpeed: 30.137904 GB/s)
      const pattern2 = /([^:]+):\s*([0-9.,]+)\s*([A-Za-z\/°%]+)/gi;
      let match2;
      while ((match2 = pattern2.exec(line)) !== null) {
        const unit = match2[3];
        const value = match2[2].replace(',', '');

        if (isValidUnit(unit) && parseFloat(value) > 0) {
          suggestions.push({
            label: cleanLabel(match2[1].trim()),
            value: value,
            unit: unit,
            confidence: 0.95,
            lineNumbers: [index + 1],
            originalText: match2[0]
          });
        }
      }
      
      // 模式3: 括号格式 (如: AverageUserSpeed(GB/s): 30.137904)
      const bracketPattern = /([^:\(]+)\(([^)]+)\):\s*([0-9.,]+)/gi;
      let bracketMatch;
      while ((bracketMatch = bracketPattern.exec(line)) !== null) {
        const unit = bracketMatch[2];
        const value = bracketMatch[3].replace(',', '');

        if (isValidUnit(unit) && parseFloat(value) > 0) {
          // 避免重复添加
          const exists = suggestions.some(s =>
            s.lineNumbers.includes(index + 1) &&
            Math.abs(parseFloat(s.value) - parseFloat(value)) < 0.001 &&
            s.unit === unit
          );

          if (!exists) {
            suggestions.push({
              label: cleanLabel(bracketMatch[1].trim()),
              value: value,
              unit: unit,
              confidence: 0.98,
              lineNumbers: [index + 1],
              originalText: bracketMatch[0]
            });
          }
        }
      }

      // 模式4: 百分比格式 (如: 95% 置信度: 95%)
      const percentPattern = /([^:]+):\s*([0-9.,]+)%/gi;
      let percentMatch;
      while ((percentMatch = percentPattern.exec(line)) !== null) {
        const value = percentMatch[2].replace(',', '');

        if (parseFloat(value) >= 0 && parseFloat(value) <= 100) {
          suggestions.push({
            label: cleanLabel(percentMatch[1].trim()),
            value: value,
            unit: '%',
            confidence: 0.90,
            lineNumbers: [index + 1],
            originalText: percentMatch[0]
          });
        }
      }
      
      // 模式5: 数据 + 单位 在同一行 (如: 30.5 GB/s, 85°C, 150W)
      const unitPattern = /\b([0-9.,]+)\s*([A-Za-z\/°%]{2,})\b/gi;
      let unitMatch;
      while ((unitMatch = unitPattern.exec(line)) !== null) {
        const unit = unitMatch[2];
        const value = unitMatch[1].replace(',', '');

        // 只识别明确的单位格式
        if (isValidUnit(unit) && parseFloat(value) > 0) {
          // 避免重复添加（可能已经被前面的模式匹配了）
          const exists = suggestions.some(s =>
            s.lineNumbers.includes(index + 1) &&
            Math.abs(parseFloat(s.value) - parseFloat(value)) < 0.001 &&
            s.unit === unit
          );

          if (!exists) {
            const label = inferLabelFromContext(line, unit);
            suggestions.push({
              label: label,
              value: value,
              unit: unit,
              confidence: 0.85,
              lineNumbers: [index + 1],
              originalText: unitMatch[0]
            });
          }
        }
      }
      
      // 模式6: 纯数字行，可能单位在其他地方（图片中的情况）
      const numberPattern = /^\s*([0-9.,]+)\s*$/;
      const numberMatch = line.match(numberPattern);
      if (numberMatch) {
        const value = numberMatch[1].replace(',', '');
        if (parseFloat(value) > 0) {
          // 查找上下文中的单位线索
          const contextLines = [
            lines[Math.max(0, index - 2)],
            lines[Math.max(0, index - 1)],
            line,
            lines[Math.min(lines.length - 1, index + 1)],
            lines[Math.min(lines.length - 1, index + 2)]
          ].join(' ');

          // 在上下文中查找可能的单位和标签
          const contextUnitMatch = contextLines.match(/([A-Za-z\/°%]{2,})/i);
          const contextLabelMatch = contextLines.match(/(Average[A-Za-z]+|[A-Za-z]+Time|[A-Za-z]+Speed|[A-Za-z]+Bandwidth)/i);
          
          if (contextUnitMatch && isValidUnit(contextUnitMatch[1])) {
            const unit = contextUnitMatch[1];
            const label = contextLabelMatch ? contextLabelMatch[1] : inferLabelFromContext(contextLines, unit);
            
            // 避免重复添加
            const exists = suggestions.some(s => 
              s.lineNumbers.includes(index + 1) && 
              Math.abs(parseFloat(s.value) - parseFloat(value)) < 0.001
            );
            
            if (!exists) {
              suggestions.push({
                label: label,
                value: value,
                unit: unit,
                confidence: 0.75,
                lineNumbers: [index + 1],
                originalText: line.trim()
              });
            }
          }
        }
      }
    });
    
    // 去重和清理
    const uniqueSuggestions = [];
    const seen = new Set();
    
    suggestions.forEach(suggestion => {
      const key = `${suggestion.lineNumbers[0]}-${suggestion.value}-${suggestion.unit}`;
      if (!seen.has(key)) {
        seen.add(key);
        uniqueSuggestions.push(suggestion);
      }
    });
    
    // 按置信度和行号排序
    return uniqueSuggestions.sort((a, b) => {
      if (b.confidence !== a.confidence) {
        return b.confidence - a.confidence;
      }
      return a.lineNumbers[0] - b.lineNumbers[0];
    });
  };

  // 验证是否为有效单位
  const isValidUnit = (unit) => {
    const validUnits = [
      // 传输速度
      'GB/s', 'MB/s', 'KB/s', 'Gb/s', 'Mb/s', 'Kb/s',
      // 时间单位
      'ms', 'us', 'ns', 's', 'sec', 'seconds', 'min', 'minutes', 'h', 'hours',
      // 温度
      '°C', '°F', 'C', 'F', 'celsius', 'fahrenheit',
      // 功耗
      'W', 'KW', 'MW', 'watt', 'watts',
      // 性能
      'GFLOPS', 'TFLOPS', 'MFLOPS', 'gflops', 'tflops',
      // 频率
      'Hz', 'KHz', 'MHz', 'GHz', 'hz', 'khz', 'mhz', 'ghz',
      // 百分比
      '%', 'percent',
      // 存储
      'GB', 'MB', 'KB', 'TB', 'gb', 'mb', 'kb', 'tb',
      // I/O
      'IOPS', 'iops', 'ops',
      // AI模型专用单位
      'tok/s', 'tokens/s', 'token/s', 'req/s', 'requests/s', 'request/s',
      'tokens', 'token', 'requests', 'request'
    ];
    
    return validUnits.some(validUnit => 
      unit.toLowerCase() === validUnit.toLowerCase()
    );
  };

  // 根据上下文和单位推断标签
  const inferLabelFromContext = (line, unit) => {
    const lowerLine = line.toLowerCase();
    const lowerUnit = unit.toLowerCase();

    // AI模型推理相关
    if (lowerLine.includes('token') || lowerLine.includes('throughput')) {
      if (lowerUnit.includes('tok/s') || lowerUnit.includes('tokens/s')) {
        if (lowerLine.includes('output')) return 'Output Token Throughput';
        if (lowerLine.includes('total')) return 'Total Token Throughput';
        return 'Token Throughput';
      }
      if (lowerUnit.includes('tokens') || lowerUnit.includes('token')) {
        if (lowerLine.includes('input')) return 'Total Input Tokens';
        if (lowerLine.includes('generated')) return 'Total Generated Tokens';
        return 'Token Count';
      }
    }

    // 请求相关
    if (lowerLine.includes('request') || lowerLine.includes('req')) {
      if (lowerUnit.includes('req/s') || lowerUnit.includes('requests/s')) {
        return 'Request Throughput';
      }
      if (lowerUnit.includes('requests') || lowerUnit.includes('request')) {
        if (lowerLine.includes('successful')) return 'Successful Requests';
        return 'Request Count';
      }
    }

    // 时间相关 - AI模型特有指标
    if (lowerUnit.includes('ms') || lowerUnit.includes('s')) {
      if (lowerLine.includes('ttft')) return 'Time to First Token';
      if (lowerLine.includes('tpot')) return 'Time per Output Token';
      if (lowerLine.includes('itl')) return 'Inter-token Latency';
      if (lowerLine.includes('duration')) return 'Benchmark Duration';
      if (lowerLine.includes('mean')) return 'Mean Response Time';
      if (lowerLine.includes('median')) return 'Median Response Time';
      if (lowerLine.includes('p99')) return 'P99 Response Time';
    }

    // GPU/显卡相关
    if (lowerLine.includes('gpu') || lowerLine.includes('cuda') || lowerLine.includes('graphics')) {
      if (lowerUnit.includes('gb/s') || lowerUnit.includes('mb/s')) {
        return 'GPU内存带宽';
      }
      if (lowerUnit.includes('gflops') || lowerUnit.includes('tflops')) {
        return 'GPU计算性能';
      }
      if (lowerUnit.includes('°c') || lowerUnit.includes('celsius')) {
        return 'GPU温度';
      }
      if (lowerUnit.includes('w') || lowerUnit.includes('watt')) {
        return 'GPU功耗';
      }
      return 'GPU指标';
    }
    
    // PCIe相关
    if (lowerLine.includes('pcie') || lowerLine.includes('pci')) {
      if (lowerUnit.includes('gb/s') || lowerUnit.includes('mb/s')) {
        return 'PCIe带宽';
      }
      return 'PCIe指标';
    }
    
    // 内存相关
    if (lowerLine.includes('memory') || lowerLine.includes('mem') || lowerLine.includes('ram')) {
      if (lowerUnit.includes('gb/s') || lowerUnit.includes('mb/s')) {
        return '内存带宽';
      }
      if (lowerUnit.includes('gb') || lowerUnit.includes('mb')) {
        return '内存容量';
      }
      return '内存指标';
    }
    
    // 温度相关
    if (lowerUnit.includes('°c') || lowerUnit.includes('celsius') || lowerUnit.includes('fahrenheit')) {
      if (lowerLine.includes('cpu')) return 'CPU温度';
      if (lowerLine.includes('gpu')) return 'GPU温度';
      return '温度';
    }
    
    // 功耗相关
    if (lowerUnit.includes('w') || lowerUnit.includes('watt')) {
      if (lowerLine.includes('cpu')) return 'CPU功耗';
      if (lowerLine.includes('gpu')) return 'GPU功耗';
      return '功耗';
    }
    
    // 性能相关
    if (lowerUnit.includes('gflops') || lowerUnit.includes('tflops')) {
      return '计算性能';
    }
    
    // 延迟相关
    if (lowerUnit.includes('ms') || lowerUnit.includes('us') || lowerUnit.includes('ns')) {
      if (lowerLine.includes('latency') || lowerLine.includes('delay')) {
        return '延迟';
      }
      return '时间';
    }
    
    // 频率相关
    if (lowerUnit.includes('hz') || lowerUnit.includes('mhz') || lowerUnit.includes('ghz')) {
      if (lowerLine.includes('cpu')) return 'CPU频率';
      if (lowerLine.includes('gpu')) return 'GPU频率';
      if (lowerLine.includes('memory') || lowerLine.includes('mem')) return '内存频率';
      return '频率';
    }
    
    // 百分比相关
    if (lowerUnit.includes('%')) {
      if (lowerLine.includes('usage') || lowerLine.includes('util')) return '使用率';
      if (lowerLine.includes('load')) return '负载';
      return '百分比';
    }
    
    // 传输速度相关
    if (lowerUnit.includes('gb/s') || lowerUnit.includes('mb/s') || lowerUnit.includes('kb/s')) {
      return '传输速度';
    }
    
    // 通用标签
    return '性能指标';
  };

  // 辅助函数：从标签推断单位
  const inferUnitFromLabel = (label) => {
    const lowerLabel = label.toLowerCase();

    if (lowerLabel.includes('duration') || lowerLabel.includes('time')) {
      if (lowerLabel.includes('(s)') || lowerLabel.includes('second')) return 's';
      if (lowerLabel.includes('(ms)') || lowerLabel.includes('millisecond')) return 'ms';
      return 's'; // 默认秒
    }

    if (lowerLabel.includes('speed') || lowerLabel.includes('bandwidth') || lowerLabel.includes('throughput')) {
      return 'GB/s';
    }

    if (lowerLabel.includes('frequency') || lowerLabel.includes('clock')) {
      return 'MHz';
    }

    if (lowerLabel.includes('temperature') || lowerLabel.includes('temp')) {
      return '°C';
    }

    if (lowerLabel.includes('power') || lowerLabel.includes('watt')) {
      return 'W';
    }

    if (lowerLabel.includes('token')) {
      return 'tokens';
    }

    if (lowerLabel.includes('request')) {
      return 'requests';
    }

    if (lowerLabel.includes('percent') || lowerLabel.includes('rate') || lowerLabel.includes('ratio')) {
      return '%';
    }

    return ''; // 无单位
  };

  // 辅助函数：清理标签文本
  const cleanLabel = (label) => {
    return label
      .replace(/\([^)]*\)/g, '') // 移除括号内容
      .replace(/^\s*[-•*]\s*/, '') // 移除开头的符号
      .trim()
      .replace(/\s+/g, ' '); // 规范化空格
  };

  // 辅助函数：获取指标分类
  const getMetricCategory = (label, unit) => {
    const lowerLabel = label.toLowerCase();
    const lowerUnit = unit.toLowerCase();

    // AI模型性能指标
    if (lowerLabel.includes('token') || lowerLabel.includes('throughput') ||
        lowerUnit.includes('tok/s') || lowerUnit.includes('tokens')) {
      return 'AI Performance';
    }

    // 请求相关指标
    if (lowerLabel.includes('request') || lowerLabel.includes('req') ||
        lowerUnit.includes('req/s') || lowerUnit.includes('requests')) {
      return 'Request Metrics';
    }

    // 时间和延迟指标
    if (lowerUnit.includes('ms') || lowerUnit.includes('s') ||
        lowerLabel.includes('time') || lowerLabel.includes('latency') ||
        lowerLabel.includes('duration')) {
      return 'Timing & Latency';
    }

    // 系统资源指标
    if (lowerLabel.includes('memory') || lowerLabel.includes('cpu') ||
        lowerLabel.includes('gpu') || lowerUnit.includes('gb') ||
        lowerUnit.includes('mb') || lowerUnit.includes('%')) {
      return 'System Resources';
    }

    // 网络和I/O指标
    if (lowerUnit.includes('gb/s') || lowerUnit.includes('mb/s') ||
        lowerUnit.includes('iops') || lowerLabel.includes('bandwidth')) {
      return 'Network & I/O';
    }

    return 'General';
  };

  const handleLineSelection = (lineNumbers) => {
    setSelectedLines(lineNumbers);
  };

  const handleMetricUpdate = (updatedMetrics) => {
    setExtractedMetrics(updatedMetrics);
    
    // 更新进度
    const confirmedCount = updatedMetrics.filter(m => m.status === 'confirmed').length;
    const totalCount = updatedMetrics.length;
    const progress = totalCount > 0 ? Math.round((confirmedCount / totalCount) * 100) : 0;
    setExtractionProgress(progress);
  };

  const handleSaveResults = async () => {
    const confirmedMetrics = extractedMetrics.filter(m => m.status === 'confirmed');
    
    if (confirmedMetrics.length === 0) {
      message.warning('请至少确认一个指标后再保存');
      return;
    }
    
    try {
      setSaving(true);
      
      // 构建保存数据
      const resultData = {
        execution_id: executionInfo.id,
        plan_name: executionInfo.planName,
        metrics: confirmedMetrics.map(metric => ({
          label: metric.label,
          value: metric.value,
          unit: metric.unit,
          confidence: metric.confidence
        })),
        extraction_time: new Date().toISOString(),
        total_metrics: confirmedMetrics.length
      };
      
      // 调用后端API保存结果
      await http.post('/api/exec/test-results/', resultData);
      
      message.success(`成功保存 ${confirmedMetrics.length} 个性能指标`);
      
      // 显示成功提示
      Modal.success({
        title: '🎉 结果收集完成',
        content: (
          <div>
            <p>已成功提取并保存测试结果：</p>
            <ul>
              {confirmedMetrics.map((metric, index) => (
                <li key={index}>
                  {metric.label}: {metric.value} {metric.unit}
                </li>
              ))}
            </ul>
          </div>
        ),
        onOk: () => {
          // 可以选择关闭页面或跳转到结果展示页面
        }
      });
      
    } catch (err) {
      message.error('保存失败: ' + err.message);
    } finally {
      setSaving(false);
    }
  };

  const handleMultiFileExtraction = (results, extractionInfo) => {
    console.log('[DEBUG] 多文件提取成功:', results, extractionInfo);

    setMultiFileResults(results);
    setExtractionConfigVisible(false);

    // 合并所有文件的内容进行分析
    const combinedContent = results
      .filter(result => result.success && result.content)
      .map(result => `=== ${result.log_path} ===\n${result.content}`)
      .join('\n\n');

    if (combinedContent) {
      setLogContent(combinedContent);
      performAIAnalysis(combinedContent);
      message.success(`成功加载 ${results.filter(r => r.success).length} 个日志文件进行分析`);
    } else {
      message.warning('没有成功获取到有效的日志内容');
    }
  };

  const confirmedCount = extractedMetrics.filter(m => m.status === 'confirmed').length;
  const totalCount = extractedMetrics.length;

  return (
    <Layout className={styles.extractionLayout}>
      <Header className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.headerLeft}>
            <ExperimentOutlined style={{ fontSize: '20px', marginRight: '8px' }} />
            <span className={styles.title}>智能结果收集器</span>
            <Tag color="blue" style={{ marginLeft: '12px' }}>
              {executionInfo.planName}
            </Tag>
          </div>
          
          <div className={styles.headerRight}>
            <Space>
              <div className={styles.progressInfo}>
                已确认: {confirmedCount}/{totalCount}
              </div>
              <Progress 
                percent={extractionProgress} 
                size="small" 
                style={{ width: '120px' }}
                strokeColor="#722ed1"
              />
              <Button
                icon={<RobotOutlined />}
                onClick={() => setTemplateModalVisible(true)}
              >
                智能模板
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={() => setExtractionConfigVisible(true)}
              >
                获取并分析日志
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSaveResults}
                loading={saving}
                disabled={confirmedCount === 0}
              >
                保存结果 ({confirmedCount})
              </Button>
            </Space>
          </div>
        </div>
      </Header>
      
      <Layout>
        <Content className={styles.mainContent}>
          <Card 
            title="📋 执行日志分析"
            extra={
              <Space>
                <Tooltip title="重新分析">
                  <Button 
                    icon={<ReloadOutlined />}
                    onClick={() => performAIAnalysis(logContent)}
                    loading={loading}
                  >
                    重新分析
                  </Button>
                </Tooltip>
                <Tooltip title="查看原始日志">
                  <Button icon={<EyeOutlined />}>
                    原始日志
                  </Button>
                </Tooltip>
              </Space>
            }
            className={styles.logCard}
          >
            {loading ? (
              <div className={styles.loadingContainer}>
                <Spin size="large" />
                <div style={{ marginTop: '16px' }}>正在分析日志内容...</div>
              </div>
            ) : (
              <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                {aiSuggestions.length > 0 && (
                  <Alert
                    message="AI分析结果"
                    description={`发现 ${aiSuggestions.length} 个潜在的性能指标，请在右侧面板中确认或修改`}
                    type="info"
                    showIcon
                    style={{ marginBottom: '16px', flexShrink: 0 }}
                  />
                )}
                
                <div style={{ flex: 1, overflow: 'hidden' }}>
                  <LogViewer
                    content={logContent}
                    suggestions={aiSuggestions}
                    selectedLines={selectedLines}
                    onLineSelection={handleLineSelection}
                  />
                </div>
              </div>
            )}
          </Card>
        </Content>
        
        <Sider width={400} className={styles.sidebar}>
          <MetricsPanel
            metrics={extractedMetrics}
            onMetricsUpdate={handleMetricUpdate}
            suggestions={aiSuggestions}
          />
        </Sider>
      </Layout>
      
      <TemplateSelector
        visible={templateModalVisible}
        onCancel={() => setTemplateModalVisible(false)}
        onApply={(template) => {
          // 应用模板逻辑
          setTemplateModalVisible(false);
          message.success(`已应用模板: ${template.name}`);
        }}
      />

      <LogExtractionConfig
        visible={extractionConfigVisible}
        onCancel={() => setExtractionConfigVisible(false)}
        onSuccess={handleMultiFileExtraction}
        initialLogPath={executionInfo.logFile}
        executionInfo={executionInfo}
      />
    </Layout>
  );
}

export default ResultExtraction; 