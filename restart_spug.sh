#!/bin/bash

# Spug 生产环境重启脚本 - 官方supervisor方式
# 更新日期: 2025-07-24

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目路径
PROJECT_ROOT="/opt/spug"

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] $message${NC}"
}

print_message $GREEN "=== Spug生产环境重启脚本（官方方式）==="

# 1. 更新代码
print_message $BLUE "步骤1: 更新代码..."
cd "$PROJECT_ROOT"
if git pull; then
    print_message $GREEN "✓ 代码更新成功"
else
    print_message $YELLOW "⚠ 代码更新失败或无更新，继续执行..."
fi

# 2. 检查是否有数据库迁移
print_message $BLUE "步骤2: 检查数据库迁移..."
cd "$PROJECT_ROOT/spug_api"
if python3 manage.py showmigrations --plan | grep -q "\[ \]"; then
    print_message $YELLOW "发现未应用的数据库迁移，正在执行..."
    python3 manage.py migrate
    print_message $GREEN "✓ 数据库迁移完成"
else
    print_message $GREEN "✓ 数据库已是最新状态"
fi

# 3. 重新构建前端（如果有变化）
print_message $BLUE "步骤3: 检查前端构建..."
cd "$PROJECT_ROOT/spug_web"
if [ ! -d "build" ] || [ "$(find src -newer build -type f | wc -l)" -gt 0 ]; then
    print_message $YELLOW "需要重新构建前端..."
    if [ -d "build" ]; then
        rm -rf build
    fi
    NODE_OPTIONS="--openssl-legacy-provider" GENERATE_SOURCEMAP=false npm run build
    print_message $GREEN "✓ 前端构建完成"
else
    print_message $GREEN "✓ 前端无需重新构建"
fi

# 4. 重启supervisor服务
print_message $BLUE "步骤4: 重启Spug服务..."
supervisorctl restart spug-api spug-ws spug-worker spug-monitor spug-scheduler

# 5. 重新加载nginx配置
print_message $BLUE "步骤5: 重新加载Nginx配置..."
nginx -t && systemctl reload nginx

# 6. 等待服务启动
print_message $BLUE "步骤6: 等待服务启动..."
sleep 5

# 7. 检查服务状态
print_message $BLUE "步骤7: 检查服务状态..."
echo ""
supervisorctl status
echo ""

# 8. 验证服务
print_message $BLUE "步骤8: 验证服务..."
if curl -s http://127.0.0.1:9001/ > /dev/null 2>&1; then
    print_message $GREEN "✓ API服务正常 (Port: 9001)"
else
    print_message $RED "✗ API服务异常"
fi

if curl -s http://192.2.111.65/api/ > /dev/null 2>&1; then
    print_message $GREEN "✓ Nginx代理正常"
else
    print_message $RED "✗ Nginx代理异常"
fi

if curl -s http://192.2.111.65/ > /dev/null 2>&1; then
    print_message $GREEN "✓ 前端服务正常"
else
    print_message $RED "✗ 前端服务异常"
fi

print_message $GREEN "=== Spug生产环境重启完成 ==="
print_message $BLUE "访问地址: http://192.2.111.65"
print_message $BLUE "管理命令: supervisorctl status|restart|stop|start spug-*"
