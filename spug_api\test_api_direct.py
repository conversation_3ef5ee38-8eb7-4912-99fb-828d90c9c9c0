#!/usr/bin/env python3
"""
直接测试监控总览API
"""
import os
import sys
import django
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'spug.settings')
django.setup()

from apps.monitor.views import get_overview
from apps.monitor.models import Detection
from django.test import RequestFactory
from apps.account.models import User
from django_redis import get_redis_connection

def test_api_direct():
    """直接测试API"""
    print("=== 直接测试监控总览API ===\n")
    
    # 检查当前的监控任务
    print("📊 检查当前监控任务...")
    from apps.monitor.models import Detection
    from apps.host.models import Host

    detections = Detection.objects.filter(is_active=True)
    print(f"发现 {detections.count()} 个激活的监控任务:")

    for det in detections:
        print(f"  - ID: {det.id}, 名称: {det.name}, 类型: {det.get_type_display()}")

    # 检查Redis中的数据
    print(f"\n🗄️ 检查Redis数据...")
    rds = get_redis_connection()

    # 查找所有进程信息key
    info_keys = rds.keys('spug:det:info:*')
    print(f"发现 {len(info_keys)} 个进程信息key:")

    for key in info_keys:
        key_str = key.decode() if isinstance(key, bytes) else key
        print(f"  - {key_str}")

        data = rds.get(key)
        if data:
            try:
                process_info = json.loads(data.decode())
                print(f"    进程信息: {len(process_info)} 个进程")
                for i, proc in enumerate(process_info[:2]):
                    print(f"      进程 {i+1}: PID={proc.get('pid')}, 命令={proc.get('cmd')}")
            except Exception as e:
                print(f"    数据解析失败: {e}")
    
    # 创建模拟请求
    print(f"\n🌐 测试API调用...")
    factory = RequestFactory()
    request = factory.get('/api/monitor/overview/')
    
    # 获取用户
    user = User.objects.first()
    if not user:
        print("❌ 没有找到用户")
        return
    
    request.user = user
    
    try:
        # 调用API
        response = get_overview(request)
        
        if hasattr(response, 'content'):
            content = response.content.decode()
            response_data = json.loads(content)

            print(f"📄 API原始响应结构: {list(response_data.keys())}")

            # 正确解析数据
            if 'data' in response_data:
                data = response_data['data']
                print(f"✅ API返回数据: {len(data)} 个监控项")

                for item in data:
                    print(f"\n📋 监控项:")
                    print(f"   ID: {item.get('id')}")
                    print(f"   名称: {item.get('name')}")
                    print(f"   类型: {item.get('type')}")
                    print(f"   状态: {item.get('status')}")
                    print(f"   进程信息: {item.get('process_info')}")

                    if item.get('process_info'):
                        print(f"   ✅ 有进程信息: {len(item['process_info'])} 个进程")
                        for i, proc in enumerate(item['process_info'][:2]):
                            print(f"      进程 {i+1}: PID={proc.get('pid')}, 运行时间={proc.get('etime')}")
                    else:
                        print(f"   ❌ 无进程信息")
            else:
                print(f"❌ API响应格式错误: {response_data}")
        else:
            print(f"❌ API调用失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_api_direct()
